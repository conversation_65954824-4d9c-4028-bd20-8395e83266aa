#!/bin/bash

# VidyaArena Setup Script
echo "🚀 Setting up VidyaArena project..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_nodejs() {
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js is installed: $NODE_VERSION"
    else
        print_error "Node.js is not installed. Please install Node.js v16 or higher."
        exit 1
    fi
}

# Check if PostgreSQL is installed
check_postgresql() {
    if command -v psql &> /dev/null; then
        PG_VERSION=$(psql --version)
        print_success "PostgreSQL is installed: $PG_VERSION"
    else
        print_warning "PostgreSQL is not installed. Please install PostgreSQL."
        echo "Installation commands:"
        echo "  Ubuntu/Debian: sudo apt-get install postgresql postgresql-contrib"
        echo "  macOS: brew install postgresql"
        echo "  Windows: Download from https://www.postgresql.org/download/"
        exit 1
    fi
}

# Check if Flutter is installed
check_flutter() {
    if command -v flutter &> /dev/null; then
        FLUTTER_VERSION=$(flutter --version | head -n 1)
        print_success "Flutter is installed: $FLUTTER_VERSION"
    else
        print_warning "Flutter is not installed. Please install Flutter SDK."
        echo "Visit: https://flutter.dev/docs/get-started/install"
    fi
}

# Setup backend
setup_backend() {
    print_status "Setting up backend..."
    
    cd backend || exit 1
    
    # Install dependencies
    print_status "Installing Node.js dependencies..."
    npm install
    
    if [ $? -eq 0 ]; then
        print_success "Backend dependencies installed successfully"
    else
        print_error "Failed to install backend dependencies"
        exit 1
    fi
    
    # Copy environment file if it doesn't exist
    if [ ! -f .env ]; then
        cp .env.example .env
        print_success "Environment file created (.env)"
        print_warning "Please update the .env file with your database credentials"
    else
        print_warning ".env file already exists"
    fi
    
    cd ..
}

# Setup Flutter app
setup_flutter() {
    if command -v flutter &> /dev/null; then
        print_status "Setting up Flutter app..."
        
        cd vidya_arena || exit 1
        
        # Get dependencies
        print_status "Getting Flutter dependencies..."
        flutter pub get
        
        if [ $? -eq 0 ]; then
            print_success "Flutter dependencies installed successfully"
        else
            print_error "Failed to install Flutter dependencies"
            exit 1
        fi
        
        cd ..
    else
        print_warning "Skipping Flutter setup (Flutter not installed)"
    fi
}

# Create database setup script
create_db_script() {
    print_status "Creating database setup script..."
    
    cat > setup_database.sql << EOF
-- VidyaArena Database Setup
CREATE DATABASE vidya_arena;
CREATE USER vidya_user WITH PASSWORD 'vidya_password_123';
GRANT ALL PRIVILEGES ON DATABASE vidya_arena TO vidya_user;

-- Connect to the database
\c vidya_arena;

-- Grant schema permissions
GRANT ALL ON SCHEMA public TO vidya_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO vidya_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO vidya_user;

-- Display success message
SELECT 'Database setup completed successfully!' as status;
EOF

    print_success "Database setup script created (setup_database.sql)"
}

# Main setup function
main() {
    echo "============================================"
    echo "🎮 VidyaArena Project Setup"
    echo "============================================"
    
    # Check prerequisites
    print_status "Checking prerequisites..."
    check_nodejs
    check_postgresql
    check_flutter
    
    echo ""
    print_status "Prerequisites check completed!"
    echo ""
    
    # Setup components
    setup_backend
    echo ""
    setup_flutter
    echo ""
    create_db_script
    
    echo ""
    echo "============================================"
    print_success "Setup completed successfully! 🎉"
    echo "============================================"
    echo ""
    echo "Next steps:"
    echo "1. Set up the database:"
    echo "   sudo -u postgres psql -f setup_database.sql"
    echo ""
    echo "2. Update backend/.env with your database credentials"
    echo ""
    echo "3. Initialize the database:"
    echo "   cd backend && npm run init-db"
    echo ""
    echo "4. Start the backend server:"
    echo "   cd backend && npm run dev"
    echo ""
    echo "5. Run the Flutter app:"
    echo "   cd vidya_arena && flutter run"
    echo ""
    echo "For detailed instructions, see setup.md"
}

# Run main function
main
