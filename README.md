# VidyaArena

A Flutter mobile application with a Node.js backend API and PostgreSQL database for user management and authentication.

## 🚀 Quick Start

### Automated Setup
```bash
# Run the setup script
./setup.sh
```

### Manual Setup
1. **Prerequisites**: Node.js, PostgreSQL, Flutter SDK
2. **Backend**: `cd backend && npm install && npm run dev`
3. **Flutter**: `cd vidya_arena && flutter pub get && flutter run`

For detailed setup instructions, see [setup.md](setup.md)

## 📱 Features

- User registration and authentication
- JWT-based authorization
- Secure password hashing
- Input validation
- Rate limiting
- CORS protection
- Environment-based configuration

## 🏗️ Architecture

```
VidyaArena/
├── backend/          # Node.js Express API
│   ├── config/       # Database configuration
│   ├── controllers/  # API controllers
│   ├── middleware/   # Authentication & validation
│   ├── routes/       # API routes
│   └── scripts/      # Database initialization
├── vidya_arena/      # Flutter mobile app
│   ├── lib/
│   │   ├── models/   # Data models
│   │   ├── services/ # API & auth services
│   │   └── screens/  # UI screens
└── setup.md         # Detailed setup guide
```

## 🛠️ Technology Stack

### Backend
- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: PostgreSQL
- **Authentication**: JWT
- **Security**: bcrypt, helmet, rate limiting

### Frontend
- **Framework**: Flutter
- **Language**: Dart
- **HTTP Client**: http package
- **Secure Storage**: flutter_secure_storage

## 🔧 Development

### Backend Development
```bash
cd backend
npm run dev  # Start with auto-reload
```

### Flutter Development
```bash
cd vidya_arena
flutter run  # Run on connected device/emulator
```

## 📡 API Endpoints

- `GET /health` - Health check
- `POST /api/users/register` - User registration
- `POST /api/users/login` - User login
- `GET /api/users/profile` - Get user profile (auth required)
- `GET /api/users/check-email` - Check email availability

## 🔒 Security Features

- Password hashing with bcrypt (12 rounds)
- JWT token authentication
- Rate limiting (100 requests/15 minutes)
- Input validation and sanitization
- CORS protection
- Helmet.js security headers
- Secure token storage in Flutter

## 🌍 Environment Configuration

### Development
- Backend: `http://localhost:3000`
- Database: Local PostgreSQL
- Flutter: Connects to localhost

### Production
- Update environment variables
- Use HTTPS
- Configure production database
- Update Flutter API base URL

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For issues and questions, please create an issue in the repository.

---

**Note**: This project replaces the previous SQLite local database with a hosted PostgreSQL database solution for better scalability and production readiness.
