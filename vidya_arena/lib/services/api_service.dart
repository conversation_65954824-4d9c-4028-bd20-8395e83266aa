import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../models/api_response.dart';
import 'token_service.dart';

class ApiService {
  static const String _baseUrl = 'http://localhost:3000/api';
  static const Duration _timeout = Duration(seconds: 30);

  final TokenService _tokenService = TokenService();

  Future<Map<String, String>> _getHeaders({bool includeAuth = false}) async {
    Map<String, String> headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (includeAuth) {
      String? token = await _tokenService.getToken();
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }
    }

    return headers;
  }

  ApiResponse<T> _handleResponse<T>(http.Response response, T Function(Map<String, dynamic>) fromJson) {
    try {
      final Map<String, dynamic> data = json.decode(response.body);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return ApiResponse<T>(
          success: data['success'] ?? true,
          message: data['message'] ?? 'Success',
          data: data['data'] != null ? fromJson(data['data']) : null,
        );
      } else {
        return ApiResponse<T>(
          success: false,
          message: data['message'] ?? 'An error occurred',
          errors: data['errors'],
        );
      }
    } catch (e) {
      return ApiResponse<T>(
        success: false,
        message: 'Network error. Please check your connection.',
      );
    }
  }

  Future<ApiResponse<UserData>> registerUser(User user) async {
    try {
      final headers = await _getHeaders();
      final requestBody = {
        'name': user.name.trim(),
        'email': user.email.trim().toLowerCase(),
        'password': user.password,
        'mobile': user.mobile.trim(),
        'dob': user.dob,
        'gender': user.gender.toLowerCase(),
      };

      final response = await http.post(
        Uri.parse('$_baseUrl/users/register'),
        headers: headers,
        body: json.encode(requestBody),
      ).timeout(_timeout);

      final apiResponse = _handleResponse<UserData>(
        response,
        (data) => UserData.fromJson(data)
      );

      if (apiResponse.success && apiResponse.data?.token != null) {
        await _tokenService.saveToken(apiResponse.data!.token!);
      }

      return apiResponse;

    } catch (e) {
      return ApiResponse<UserData>(
        success: false,
        message: 'Network error. Please check your connection.',
      );
    }
  }

  Future<ApiResponse<UserData>> loginUser(String email, String password) async {
    try {
      final headers = await _getHeaders();
      final requestBody = {
        'email': email.trim().toLowerCase(),
        'password': password,
      };

      final response = await http.post(
        Uri.parse('$_baseUrl/users/login'),
        headers: headers,
        body: json.encode(requestBody),
      ).timeout(_timeout);

      final apiResponse = _handleResponse<UserData>(
        response,
        (data) => UserData.fromJson(data)
      );

      if (apiResponse.success && apiResponse.data?.token != null) {
        await _tokenService.saveToken(apiResponse.data!.token!);
      }

      return apiResponse;

    } catch (e) {
      return ApiResponse<UserData>(
        success: false,
        message: 'Network error. Please check your connection.',
      );
    }
  }

  // Get user profile
  Future<ApiResponse<User>> getUserProfile() async {
    try {
      final headers = await _getHeaders(includeAuth: true);

      final response = await http.get(
        Uri.parse('$_baseUrl/users/profile'),
        headers: headers,
      ).timeout(_timeout);

      return _handleResponse<User>(
        response, 
        (data) => User.fromMap(data['user'])
      );

    } catch (e) {
      return ApiResponse<User>(
        success: false,
        message: 'Network error. Please check your connection.',
      );
    }
  }

  // Check if email exists
  Future<ApiResponse<bool>> checkEmailExists(String email) async {
    try {
      final headers = await _getHeaders();

      final response = await http.get(
        Uri.parse('$_baseUrl/users/check-email?email=${Uri.encodeComponent(email)}'),
        headers: headers,
      ).timeout(_timeout);

      return _handleResponse<bool>(
        response, 
        (data) => data['exists'] as bool
      );

    } catch (e) {
      return ApiResponse<bool>(
        success: false,
        message: 'Network error. Please check your connection.',
      );
    }
  }

  // Logout user
  Future<void> logout() async {
    await _tokenService.deleteToken();
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    String? token = await _tokenService.getToken();
    return token != null;
  }


}
