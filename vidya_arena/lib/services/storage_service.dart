import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  static SharedPreferences? _prefs;

  StorageService._internal();

  factory StorageService() => _instance;

  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  Future<SharedPreferences> get prefs async {
    if (_prefs != null) return _prefs!;
    await init();
    return _prefs!;
  }

  // User storage methods
  Future<bool> saveUser(User user) async {
    try {
      final preferences = await prefs;
      
      // Get existing users
      List<User> users = await getAllUsers();
      
      // Check if email already exists
      if (users.any((u) => u.email == user.email)) {
        return false; // Email already exists
      }
      
      // Add new user with auto-generated ID
      final newUser = User(
        id: users.length + 1,
        name: user.name,
        dob: user.dob,
        gender: user.gender,
        mobile: user.mobile,
        email: user.email,
        password: user.password,
        createdAt: user.createdAt ?? DateTime.now().toIso8601String(),
      );
      
      users.add(newUser);
      
      // Save updated users list
      List<String> usersJson = users.map((u) => jsonEncode(u.toMap())).toList();
      await preferences.setStringList('users', usersJson);
      
      if (kDebugMode) {
        print('User saved successfully: ${newUser.email}');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error saving user: $e');
      }
      return false;
    }
  }

  Future<List<User>> getAllUsers() async {
    try {
      final preferences = await prefs;
      List<String>? usersJson = preferences.getStringList('users');
      
      if (usersJson == null) return [];
      
      return usersJson.map((userStr) {
        Map<String, dynamic> userMap = jsonDecode(userStr);
        return User.fromMap(userMap);
      }).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting users: $e');
      }
      return [];
    }
  }

  Future<User?> getUserByEmail(String email) async {
    try {
      List<User> users = await getAllUsers();
      for (User user in users) {
        if (user.email == email) {
          return user;
        }
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user by email: $e');
      }
      return null;
    }
  }

  Future<bool> authenticateUser(String email, String password) async {
    try {
      User? user = await getUserByEmail(email);
      if (user != null && user.password == password) {
        // Save current user session
        await saveCurrentUser(user);
        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error authenticating user: $e');
      }
      return false;
    }
  }

  Future<bool> emailExists(String email) async {
    try {
      User? user = await getUserByEmail(email);
      return user != null;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking email existence: $e');
      }
      return false;
    }
  }

  // Session management
  Future<void> saveCurrentUser(User user) async {
    try {
      final preferences = await prefs;
      await preferences.setString('current_user', jsonEncode(user.toMap()));
    } catch (e) {
      if (kDebugMode) {
        print('Error saving current user: $e');
      }
    }
  }

  Future<User?> getCurrentUser() async {
    try {
      final preferences = await prefs;
      String? userJson = preferences.getString('current_user');
      
      if (userJson != null) {
        Map<String, dynamic> userMap = jsonDecode(userJson);
        return User.fromMap(userMap);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting current user: $e');
      }
      return null;
    }
  }

  Future<void> logout() async {
    try {
      final preferences = await prefs;
      await preferences.remove('current_user');
    } catch (e) {
      if (kDebugMode) {
        print('Error during logout: $e');
      }
    }
  }

  // Clear all data (for testing)
  Future<void> clearAllData() async {
    try {
      final preferences = await prefs;
      await preferences.clear();
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing data: $e');
      }
    }
  }
}
