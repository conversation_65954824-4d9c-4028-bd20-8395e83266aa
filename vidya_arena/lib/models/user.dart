class User {
  final int? id;
  final String name;
  final String dob;
  final String gender;
  final String mobile;
  final String email;
  final String password;
  final String? createdAt;

  User({
    this.id,
    required this.name,
    required this.dob,
    required this.gender,
    required this.mobile,
    required this.email,
    required this.password,
    this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'dob': dob,
      'gender': gender,
      'mobile': mobile,
      'email': email,
      'password': password,
      'created_at': createdAt ?? DateTime.now().toIso8601String(),
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'],
      name: map['name'],
      dob: map['dob'],
      gender: map['gender'],
      mobile: map['mobile'],
      email: map['email'],
      password: map['password'],
      createdAt: map['created_at'],
    );
  }

  @override
  String toString() {
    return 'User{id: $id, name: $name, email: $email, mobile: $mobile, gender: $gender, dob: $dob}';
  }
}
