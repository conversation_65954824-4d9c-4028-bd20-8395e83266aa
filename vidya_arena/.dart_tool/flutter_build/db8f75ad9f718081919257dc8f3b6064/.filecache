{"version": 2, "files": [{"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/base_request.dart", "hash": "01d9ad3c8c89b65f3180229081a95952"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "6b06971a44e8eed786f2f06388d0580d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/number_parser_base.dart", "hash": "39348131fc86fb08a42dd6b2d1b16bf0"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "ab91622a9d9c558bb65f0f06b904d873"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "60d167d34050e1468a18e6a768d9d2bc"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "360dd05d7395f54f7f785d6f8c36a191"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/debug.dart", "hash": "17fec0de01669e6234ccb93fc1d171f2"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "942fbfca7541358613467c640e1ca6cb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "8d4df3ef11f873038812b16364638706"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/text_painter.dart", "hash": "d6345e12daac6b13e85a13629c3fddc7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomation5.dart", "hash": "3681275c274b0e2b2c9dc14ecc273c1a"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "435a09654d97bb25c33b68b9cde0a585"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_shcore_scaling_l1_1_1.g.dart", "hash": "1c0e59efab3b5ae568836b9fa5a3675d"}, {"path": "/opt/flutter/packages/flutter/lib/src/physics/tolerance.dart", "hash": "f75f31535e16b018e2a5f9a968b7254c"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "fb3f068735531a31f3d1253216051136"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/shadows.dart", "hash": "c4119b97c9830ac751fca7f2c7989f6b"}, {"path": "/opt/flutter/bin/internal/engine.version", "hash": "fc1cb0ff2fed4e2b58f3dae95965dc5c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/span_exception.dart", "hash": "c39101179f8bdf0b2116c1f40a3acc25"}, {"path": "/opt/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "d2fafa799f53bac4cb844e60b40a10f7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationselectionpattern2.dart", "hash": "2783f528d559449fbd0b97561717c83d"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/framework.dart", "hash": "8f3d0a0b22c3d0ada7405e15eb0e7fb5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/shell32.g.dart", "hash": "77833f9ce93791f664316db43a55505c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationwindowpattern.dart", "hash": "0d790476d9ddbae00b9e3f0076902498"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "a0a1a162853c04dfcdb92b1a910488b7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/multipart_file_io.dart", "hash": "8830333c78de58ad9df05d396b651ef7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/src/span_scanner.dart", "hash": "e6c901b6ad02eac173f31cc971b5b703"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/opt/flutter/packages/flutter/lib/services.dart", "hash": "046141d90f3922d04cc8e19212de421c"}, {"path": "/opt/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "84bcf5111497cf7e8f1447797932cbf0"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/alignment.dart", "hash": "ff44ff979a414bb8029a4e8b205d2479"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "b79eb4cc037c18412c9e228e1974783f"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/ink_well.dart", "hash": "b064d05257802d1c2555867b7817d23b"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "5d34c419faa453f50535c81a93de00d0"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "0759c0203bc8cd82a7b99d5683673a32"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/contrast/contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system_entity.dart", "hash": "67918403456e9e1c17b3375ea708292c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/lib/src/messages_async.g.dart", "hash": "2bd174cad1b04e4cca9ba7ac37905e5d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishellservice.dart", "hash": "b7690366684d9173683d36992173f7a6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_directory.dart", "hash": "18b0559a8cbfb3b3a3d34bbbea4669c7"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/color_scheme.dart", "hash": "8f094e8fb77987b0922b558b2fd22102"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "aeaa12c1af305eb8e588f3b7bec09ab1"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/print.dart", "hash": "824dffb7b5c1cc401a975820f0085fa7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/win32.dart", "hash": "a2afa1345e294f0beeb9a776908eab25"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "3fa4c89a1c19c846cce6950ff665c20a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/lints-4.0.0/LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudiosessioncontrol2.dart", "hash": "18ce35bef6b656745428776b3aaaf4ca"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/multipart_file.dart", "hash": "ad139ffd36c17bbb2c069eb50b2ec5af"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/constants.dart", "hash": "be94b8f65e9d89867287dabe5ea1dff1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/magnification.g.dart", "hash": "e950e207ecdcf1d767721554751c6673"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/chip.dart", "hash": "aca1e7d1415fbff33606680b7d276d47"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "909cb251e671fa02581480629642a663"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/icons.dart", "hash": "81c893b7b7339a7d3d6c16614e71163b"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "hash": "8518b1367156079d21cbecf7217d2152"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/date_format_field.dart", "hash": "53b1a2074650b8f2808e620e2b9ddc41"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/location_mixin.dart", "hash": "6326660aedecbaed7a342070ba74de13"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "af3cac4b25350f32615ddef14a0beb6c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/src/generated/unicode_glyph_set.dart", "hash": "cdb411d670a094822c46ead81fc1c4f7"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "2c0bb1b05272ab6b5f631f34c4068679"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_string_codec.h", "hash": "5edcd4ae8b7d3acb2e56bf8bfad75d4d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwbemlocator.dart", "hash": "0183b13f6e13fe4c255b09236e142882"}, {"path": "/opt/flutter/packages/flutter/lib/gestures.dart", "hash": "55324926e0669ca7d823f6e2308d4a90"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "6c4f7ea6fca95237c26fda84192bc406"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationselectionpattern.dart", "hash": "89afb95565b4d1eca335d4b9b4790212"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/dropdown.dart", "hash": "5711ec38f26bd1c2412a9a943a40d4fb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/location.dart", "hash": "fb2c02d4f540edce4651227e18a35d19"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "1b935c75a1104936d71c6a3c0346c7a1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.5/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/_platform_io.dart", "hash": "bf6d84f8802d83e64fe83477c83752b4"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/text_style.dart", "hash": "d2c684f89d90661960c497d7f4faa906"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "bee3197c0ad106b501694062a10e457a"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/events.dart", "hash": "c0fcec4e410b3a54af8d2c09e4f6ef77"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/ink_splash.dart", "hash": "2568c82b17a02e29a61c5e03a4eacefe"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/_timeline_io.dart", "hash": "90f70ffdd26c85d735fbedd47d5ad80b"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/slider.dart", "hash": "e901ff12abcd3dd4ca3b79cc732d1b1d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/base_client.dart", "hash": "32a40215ba4c55ed5bb5e9795e404937"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE", "hash": "bfc483b9f818def1209e4faf830541ac"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "87e638fbc5e15e8d93ef84462a09bcf5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudiocaptureclient.dart", "hash": "98c8a48ba3ece7573f6f3a9bfde19840"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/streamed_response.dart", "hash": "a004396fa64ff2163b438ad88d1003f4"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "e0e33434911ce4e90380de00d4f00671"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishellitemfilter.dart", "hash": "3fb5dd9d7f42a9e619dd81d5bbead392"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/multipart_request.dart", "hash": "5692636576c4bec471fd3a1275f08525"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/test/test_flutter_secure_storage_platform.dart", "hash": "362bf1b65ae84f1129622a8814a50aad"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/path.dart", "hash": "157d1983388ff7abc75e862b5231aa28"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "074b866f17aee09c76583b075e83cb8c"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "72c0cf2358f026290fb717e74a910900"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/abortable.dart", "hash": "72aa3452833246a4d22c084e75fb93c3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.2.5/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ifileopendialog.dart", "hash": "e1b16ab85c86942cde8fabfa972fba9e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationmultipleviewpattern.dart", "hash": "6a7998938486af5d266f1b9072166647"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/asset_manifest.dart", "hash": "86b4658ee32f155efb8c122ef85911ef"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "164687f5f7beb0842486a60729330e3d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/image.dart", "hash": "8d7a3417ced5623477f0ae66b4693574"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "eded57cbe50a1ee3c706e7f683e68222"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_standard_message_codec.h", "hash": "c9ddf466fd4c06d0991614cd26dd74c5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "hash": "b1ebe2e5cdcee36b19b606b37a12cc38"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/linear_border.dart", "hash": "1916c4e665bb480220d0e6dce3b9400f"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "89862172ecfdefb923b68111e9a86fa1"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/scale.dart", "hash": "4046f679f31351f52629d1b9f22e8b6c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement6.dart", "hash": "e2688ec0f1c08b36b90a60cddc63b384"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishellitemarray.dart", "hash": "40abc849ae2322b6e6a63d567f952f1d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/flutter_secure_storage_windows.dart", "hash": "141745c6e29022622def8ba527cfd60c"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/resampler.dart", "hash": "780826ab1f1e8af513298cd5b5bca297"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imetadataimport2.dart", "hash": "9cea354b06cd8542da4dd38ff9fc01e9"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "e4bd74894ed2108af4000673a2a92326"}, {"path": "/opt/flutter/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "e5bfa9fa388f0c5d8a91c7d5bd969b19"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/team.dart", "hash": "f6c6b31745eec54a45d25ffe6e5d7816"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "c9c0ff593fcabc29c3234b4e1bf2ac38"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/picker.dart", "hash": "0a816e62151c063e79b644989abe3075"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/_isolates_io.dart", "hash": "b5439c33692d13cbf7df2d19d9713345"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/src/string_scanner.dart", "hash": "f158ffadca730ab601c60307ba31a5e4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/irunningobjecttable.dart", "hash": "03e32ac40b7907db555eec5ac3a5dab5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_datetime_picker_plus-2.2.0/lib/src/i18n_model.dart", "hash": "d6ce30b42eca6dea57ce6eb1a9a9d55a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwebauthenticationcoremanagerinterop.dart", "hash": "da6fd295116b361d1a0258580d3db629"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestreader3.dart", "hash": "d71f66fa79f435e0e9b2a8152443a331"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/winmd_constants.dart", "hash": "0cfcbe0ce66e9725eacd8c5fbc6f604a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/netapi32.g.dart", "hash": "1c6d490a13baec49a9edb03d5fb8a00e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "7e3157d6dcf4be11dd91edc6077e6401"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/combined_wrappers/combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/capabilities.dart", "hash": "c845d2f5cf6caad03afdca9d2aa141e7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ipersistfile.dart", "hash": "d27d71d2351cdb9c560055671b5ad215"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "8436323dbb52826a1c0e7b504bc6eb5e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtextrange.dart", "hash": "8f76417391b910fe0956d6404b59e144"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "96bce067da5564de27f19f050360860b"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/carousel.dart", "hash": "22dc080522882b010d90c7010b5aeba9"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "9568f208277ebd33bf390ffdee55c753"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudiosessionenumerator.dart", "hash": "befc59cd40e14d926671211e72495596"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/chip_theme.dart", "hash": "4eb5a88d2cb5e6153b082794c6d39409"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/banner_theme.dart", "hash": "15951ad2d184fb64e0327b35f1ce65df"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "5d93a9e5daf8f93e7820b5a2d1fa89d3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ipropertystore.dart", "hash": "2e62c409a0c6ea9effbb7a045742e1b4"}, {"path": "/opt/flutter/packages/flutter/lib/src/dart_plugin_registrant.dart", "hash": "44b8efa69ec831d1a0ce74c20ecc27b4"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "25f82e13ae2a60861c029aed5f4d8c92"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/search_anchor.dart", "hash": "28dc34f687478a2897fafbaafa053b92"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/tap.dart", "hash": "a050a2931c4a02c78e8216226cee6eba"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/lib/src/stopwatch.dart", "hash": "f38a99a51f4062e7861bb366f85265d5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/extensions/set_ansi.dart", "hash": "d30eba29d046c1a8b7f029838de6e49f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/src/charcode.dart", "hash": "b80f25d51570eededff370f0c2b94c38"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationproxyfactory.dart", "hash": "7068099dc46731641110788c3b3e1bdc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispvoice.dart", "hash": "ace74499f232b87549db3ce1828579ca"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/bstr.dart", "hash": "af04c2a11aa95d2e4b86600b33d0957c"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "7776c5eaa171bb5e03d1945d85354f49"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system.dart", "hash": "c23a0415bdaf55efdf69ac495da2aa9b"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/decorated_sliver.dart", "hash": "190d05ecf34fbb2fd698148bc68cd5b4"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_json_message_codec.h", "hash": "b550bdc7d3190030715e0fe3b4b7f014"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "a7d0241b77157594463b3f72e282b2f3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationpropertycondition.dart", "hash": "82e0e5b8ffeefc064a87f7990e0585b0"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "b5dae33166e22586a2d2fd15b559099b"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/media_query.dart", "hash": "b2a01f75c9e7125dda3e9461005b5765"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispeechobjecttoken.dart", "hash": "47cee6326ea5f9f09e1247e2930199e2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwinhttprequest.dart", "hash": "b44c83e3276e2ebad7c43ed8d7beae72"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "7e7b2010d6453107351c17753d81b0b2"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/colors.dart", "hash": "34b8a771ced62ddab9512678e7fe3fd2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/uxtheme.g.dart", "hash": "30d51f71b24984c4980f3f3c13df8190"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "872203d79024fa48a492a03eb0708499"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/text_field.dart", "hash": "d68d5ad46a579bc5f35d3da1c2f915ca"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationscrollitempattern.dart", "hash": "b09f09d05be41a57a141f88709700efd"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_basic_message_channel.h", "hash": "6cba49677d30f16cadf943f2648123fc"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "b2da3cfd818121daf62f41f3224c8a9f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-4.0.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/http_parser.dart", "hash": "b76ebf453c4f7a78139f5c52af57fda3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/idispatch.dart", "hash": "04722e21ad1b67baca7f75a984b0d2f6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishelllink.dart", "hash": "8b90b8fa4eae6234d9cdad3987f9faf3"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "799a4ef2a4bf9f5a72c65bac4ecf23a4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/case_insensitive_map.dart", "hash": "b7daa46d4dace857514806769032077d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "e06184900e9722a899299b08b5b1d95c"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "hash": "1f442d376af9a31939dd759498712154"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "ca2f231e73aa51c866ef096e66d46cf2"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/borders.dart", "hash": "a5f06e177b702e5089a03bb9d18fc7fe"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "537b299919c5bd3621a1af62db0c07a1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/lib/src/utils.dart", "hash": "e85b4f3cf370581b3ef11497a9a5bce3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/grapheme_clusters/breaks.dart", "hash": "359388897ae53df8791213c31ef05fe6"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "7826320e6f3daff8567f45add54c141d"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/lib/main.dart", "hash": "f5ba26a3f102bd16e55d8abc1cbae4d5"}, {"path": "/opt/flutter/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/back_button.dart", "hash": "035b8d3642fa73c21eafbee7851cc85d"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "994baa57aed6041f4f36e7119938923a"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "033f66ce41fadd1cb1e04ea24214f9c3"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "f203c0a13342dd79b78b56ff66fe665b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/src/line_scanner.dart", "hash": "e8cea99d6204f5bfb4d0e59002c95e39"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/opt/flutter/packages/flutter/lib/src/semantics/binding.dart", "hash": "31f32173a8983ae7bddd822a3e0e48d1"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "8288239ccc449f5dec9f381298c92c1d"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/popup_menu.dart", "hash": "605a2aef24ebcbd88042fc543e69a82a"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/message_codec.dart", "hash": "3b5bc5c0df6b6e1abb26877f612b2242"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "dfe858a6ed0de97f6c6de45176f474d4"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/list_body.dart", "hash": "0713268d2f4a4fe1a926f69964bcd39a"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/browser_context_menu.dart", "hash": "6a35dac0f777e7dd228bde492c4089b2"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "53870a85433563b929e07964cff0d2c2"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "22c35af71293a579bba619b03228367c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestreader5.dart", "hash": "a938094da69cf329b021d7351a0860fa"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "819e1a61d059020752de65cd6e5b8466"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestreader2.dart", "hash": "a109a0fbd62a37b4cf3b416da4411578"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/io_client.dart", "hash": "e792b35686d28f5a239264b5b791c0cd"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "dd134142f6edb06d6ad1ebc0d27fb524"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/theme.dart", "hash": "f0561e97f70c3b1cf5f113f21d51fa39"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/.dart_tool/flutter_build/db8f75ad9f718081919257dc8f3b6064/native_assets.yaml", "hash": "e7fd2fda36f01436b831ca47fe61fec3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iclassfactory.dart", "hash": "cd0faf95b7346ac8469c545bef368396"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/wrap.dart", "hash": "21262cfe42ea14c1c1b45867971224d7"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/radio_theme.dart", "hash": "1d2e0b9bdfb7d8463b27b487bf96ad46"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_json_method_codec.h", "hash": "c31254629784a8fd48a5ec671df85e64"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationboolcondition.dart", "hash": "96cd038c21e3727eb6325be0268a7ed6"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/feedback.dart", "hash": "268c67f634fffcd852c6fc020d6ed0fe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/options.dart", "hash": "e64d63aabc0975a7e9fdb384598c2f8f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/colors.dart", "hash": "c517fb54b3d66b22988ad7c8d07c6f53"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/request.dart", "hash": "c4b5de17270534014eb846299d500eb5"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "e7651e730f1ce3e0a0b87ac950fcce68"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/debug.dart", "hash": "e3e4ea8878b2a7a9cc04137081ae1617"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "87f486583cb723b203e4e0d12d1958a1"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "2c42d9101e59a8e036ec031c0aeaaf08"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/pop_scope.dart", "hash": "3a11b2e3469f6d7a7d722f6df2c59dd8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationexpandcollapsepattern.dart", "hash": "4a909f493f4dd8dfb93d3a3d4843bd77"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/sliver_tree.dart", "hash": "d6d7e025154dccf2e3d0b12eb073f93a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/types.dart", "hash": "13e6a7389032c839146b93656e2dd7a3"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "30a8a2a67dcb5b0a7969ce9bf1509129"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationsynchronizedinputpattern.dart", "hash": "0a1c3f1481c65ee1018b56fe8d8b84ef"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system.dart", "hash": "3120b9b427a566f796573ee37167c026"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/src/utf8.dart", "hash": "3b21907d68a2e99afa8e4103f6a72f78"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomation3.dart", "hash": "c5d7abe9da153df1f3d9d7754b91c0fb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationspreadsheetitempattern.dart", "hash": "f1cfa3a69ee743157de8de4ccdf51b58"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/font_loader.dart", "hash": "8a899256e5ac320579b269ee9b2567a8"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "faf60c9ef2ac54223911b10e9cf69c29"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_link.dart", "hash": "733eb3422250897324028933a5d23753"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/macos_options.dart", "hash": "ef56d0c30c2ebbf770de5c7e9cd6f6a7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuri.dart", "hash": "7531be50f5bc7d9a762e8842525fc199"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "58910ceafe966e76293cc267537cdc13"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "ac08cb84358e3b08fc1edebf575d7f19"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/intl.dart", "hash": "f0dd0e0193ab6bc6a1dc2a6cf6e1cd6b"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_pixel_buffer_texture.h", "hash": "fb73b65a52e89c14449cd8524058187b"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/divider.dart", "hash": "7397ee35fbfd4feddf487df2023f0ffa"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "81f395ba7a262f5e3f75cc8ce6580d0b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudioclock2.dart", "hash": "36e63388665f9d5f335135824e300cae"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding.dart", "hash": "328ff975234df68963cb19db907493ff"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "5f94dbea71a53ba72600c479a41fa013"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/magnifier.dart", "hash": "f6a7d78c53bba84751bcdff0a232e6a9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/icudtl.dat", "hash": "692337664e861ad322138061132dddc6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/boundary_characters.dart", "hash": "9d1525a634d27c83e1637a512a198b4f"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "0373ba3e37bb0de4333d914284042952"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/scarddlg.g.dart", "hash": "a40d6cf5dd5de2536012a2ab2690e67e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/comctl32.g.dart", "hash": "d847eca68c58e6b76393b62dc26a7c0a"}, {"path": "/opt/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "d4634256b002bc534042b99ffbdde402"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "5eaed6b1fcf32a11b53e5dcf27ae101c"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "76f393e988aadd48d101cbdb0255434f"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "d23bcea39c8a0ddcf5600a01de0d4bf9"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/key.dart", "hash": "c214cda31dee52ae8cbe8853acc2b7ac"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/filter_chip.dart", "hash": "2d7c9fe1f427f655c48bac29cbf4ac3c"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_event_channel.h", "hash": "defbac680fc3c78d98ef61319ff0dbb6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0/lib/meta_meta.dart", "hash": "8b83501f9451392bceda63c9281db57d"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/selection.dart", "hash": "35b81a5b6064d11e63b86458f991fbf4"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "b869c4e930ab3313f9b1d196d532d3dc"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "5bae94050956f893609adf91da0b7e13"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/grid_tile.dart", "hash": "b526e1fcb69f0ca9df233cd2fb6e69a9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispeechvoicestatus.dart", "hash": "f242cfdba2fc6ad938c53befa4c2050c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_core_winrt_string_l1_1_0.g.dart", "hash": "bb40d6ae10f99afb1b498e44b75f9a3b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/immdevicecollection.dart", "hash": "5c53c4dc5952c49c1b6ccb65674d9072"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/text_boundary.dart", "hash": "51f2a9874a4ed255b69aeb0c3feb1903"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/src/hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "8df5a0fc260d13ce415e2262527a1f8c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestapplication.dart", "hash": "3dc4006aab4c069db52d46f59e8262fa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationgriditempattern.dart", "hash": "f558b0876d2ee3eb7fc5b350a5ef85e7"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "9638263938be80353a032c8e789eb692"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/_html_element_view_io.dart", "hash": "54d59a18ed489222e79e19304ca89cc9"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "c03374f2c27987a9929d2128490ae921"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispnotifysource.dart", "hash": "c126b73764228fafd6b80ed5e2d7ff0f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/propertykey.dart", "hash": "6b00c4c5c720216a682e1018fb591aa3"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "cfad5d08fc946a2e0a67e46bf582130c"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "03b0f3319b7390e1f3928ad3e3e544a8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/ntdll.g.dart", "hash": "80549b960bc9c7fd9dad05aa69b7d9b2"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/switch_theme.dart", "hash": "444589d7a3a418a8388003283b096007"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "53af78690831d6aeb88928d8270c21ee"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_random_access_file.dart", "hash": "8584e5707c45dd6bdd567a10dfd8cd0d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/extensions/set_string_array.dart", "hash": "dce5e400c1f0958583196f9db05de7b9"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "15378441d5acd43ee5e67372183251e2"}, {"path": "/opt/flutter/packages/flutter/lib/src/animation/curves.dart", "hash": "5d9eccb0fcbc01b2c727d80f27c5631b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationitemcontainerpattern.dart", "hash": "85a9bfffa1576a9d933113d39528e24b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/functions.dart", "hash": "a12fc767bd933ecc3bbdd69f597ed3cf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "27006efbb2180e9e1afb93a52e89dbe8"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/text_selection.dart", "hash": "de3213b3d5bc998d1d921b4ce782f91f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishellitemresources.dart", "hash": "6f452535b56a9cdc6bc36bd647963dca"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "65b13c835848186d2090e77073d3c7ff"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/constants.dart", "hash": "a22042c948166ba677133268fafc4b41"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/build/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement8.dart", "hash": "2598a130fc6437cc87f8efb150561b60"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/undo_manager.dart", "hash": "186ae135e697551ae5882b55892bbb61"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/opt/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/search_view_theme.dart", "hash": "db01043236a4e15ffd4e3d8fad4c7cac"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/span.dart", "hash": "b7c2cc8260bb9ff9a961390b92e93294"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ipersistmemory.dart", "hash": "06bcab18a6206389adfe991144246ffc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtextrange3.dart", "hash": "1b5fd1f26a29d303d480169a8310b991"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtextpattern2.dart", "hash": "7c3e512b5c20c07ddded2fb71eadd848"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "hash": "b90ed671e7e766e8a27de8544ddbdcf4"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "76e270c31be8244f4a49b954bba9c76d"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/localizations.dart", "hash": "4f4fcae47233bec91490b2c694f410d3"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h", "hash": "5a5b80bcf4d72767c5fee74693a1140e"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "9f13e4907afe8f2f9ed0576085d8d0c6"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/geometry.dart", "hash": "5c89cc28cd666aa1708dffaff6cc8c35"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudiosessionmanager2.dart", "hash": "0aea2ad4289b60950d9a467b0e03e80e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/source_span.dart", "hash": "9f2eb24284aeaa1bacc5629ddb55b287"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imoniker.dart", "hash": "e6febe06d728a39b4945898e0b1294d5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ifilesavedialog.dart", "hash": "a629548f10bfeaa42dfecec77c11b6f7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/texture.dart", "hash": "3c7543874ccaad16712efd4e0249db70"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/drawer_header.dart", "hash": "1786653a5a86ec6255f79137a3a33755"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/inetwork.dart", "hash": "d2bb1791822e1c17a18ea8f306180296"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/winscard.g.dart", "hash": "77ba184cb297cb10af7a4e645e26b0ef"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/stepper.dart", "hash": "2905f1d86a6968256c390e989a477413"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/span_mixin.dart", "hash": "89dc3f84db2cd1ea37e349fdb1de09bb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/exception.dart", "hash": "9011b30a404dec657806a780b55d0610"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/container.dart", "hash": "e41783201fd039e1336413dd423a5796"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "4235d162e6bfe281428a9a62a1077806"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "2c6d411ab41668e38c9c7e50dff8980b"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "hash": "a0e89676ccae6cf3669483d52fa61075"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_core_winrt_l1_1_0.g.dart", "hash": "41bc035ab11c30618d860e3d24e2c4ef"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/callbacks.dart", "hash": "86781b32fca02e40f75c1b196e60fe4b"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/date.dart", "hash": "467e7592ed2562b6ebc43d62b1015271"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "hash": "b8e14be092255a62d690c39bef984338"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/basic_types.dart", "hash": "3126d82e17646add4e00c2099ec262ee"}, {"path": "/opt/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "hash": "a4e3123f76e135cc40ea3aa0efd2e558"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "c43806aa723e38c3c106b7655b02eabc"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "3bdf4135a561f156f34a8ce9375819ea"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/slider_theme.dart", "hash": "314b4e3c61b5f1998e51519f9d412beb"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/badge.dart", "hash": "2aca9cb812aa2f6f590b65b326ed333e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ipersiststream.dart", "hash": "7c0ee8dc84c442f69b0970bb8534d740"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "3f1936af23dbdc313352c2213f4c2dfb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/wlanapi.g.dart", "hash": "29247603a535c298681d43412512fd53"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestreader6.dart", "hash": "693ddae25fe758b1b3329d7d0ed5a005"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "0cae216bb3fa19f2f716c7f416500acc"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "b12f18fd97ffec06b763749adcd080be"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "9d63de715fbdfcbad9064ab771762145"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_datetime_picker_plus-2.2.0/lib/flutter_datetime_picker_plus.dart", "hash": "52e529bede5d366f1c787a9f37c657fe"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/stack.dart", "hash": "3fb251a2c503ed05e490e8bf688e4fae"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienumwbemclassobject.dart", "hash": "9419b7e38f497126339e8cd2ccba9e66"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "ac5fe86ab9ecbd33f878f0a580f3bfa7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/file.dart", "hash": "dcef90946d14527736cde04a54d334db"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/text_form_field.dart", "hash": "ed3dac05e5ff3d7e183323d52491c48e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispeechbasestream.dart", "hash": "095d62c8e0367fb3c65fa8c828e95c4e"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/binding.dart", "hash": "d9b79784fbfdc586f6b715fb11537767"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement5.dart", "hash": "e053a966b20fda12dc7d24e0f56c845a"}, {"path": "/opt/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "b00868737b95fe1ac169010b28a8d12b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/io_streamed_response.dart", "hash": "f179ed2f20226c436293849c724b2c4d"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "d10317bd2ff80b1a8f0f01907d62334c"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/switch.dart", "hash": "f37381ef6280c57820b3aa6cbccb1f31"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "55ae9ec969fc6f2f12ba4b96872fc552"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/predictive_back_event.dart", "hash": "42808d0d2fbb61569f4cb043ee4ed594"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/date_picker.dart", "hash": "fa3cf16f88096f2239ac79afa6bf6c1d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/extensions/set_string.dart", "hash": "097e09840cc00325fdbebaacd05f4827"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/drawer.dart", "hash": "be15261e73d394f99ecb0d5e609aafac"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h", "hash": "c31254629784a8fd48a5ec671df85e64"}, {"path": "/opt/flutter/packages/flutter/lib/widgets.dart", "hash": "8c6dc36f670f9b9a09f5f9747abd11e5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/equality.dart", "hash": "4cbe8ed92ec76b5cd80e685ba71acdb4"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/icudtl.dat", "hash": "692337664e861ad322138061132dddc6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/date_computation.dart", "hash": "37837bd1379e66f38e4a7775b6084d0e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienumnetworks.dart", "hash": "c07567abbc3cd64d4f3175c3e142da55"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_texture.h", "hash": "d1894650bbfe9ba65c6f0b195645a9a5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudiostreamvolume.dart", "hash": "eb9a74dc716d537ceafdd2a40b884df5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/file.dart", "hash": "51ffa7b452686eecd94ed080a1da4275"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "0a2cf42cdd64530e5ca9a120eda90f12"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "b87bce461399faa5b57c569a2fbfdc0e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienumvariant.dart", "hash": "ee434a4fa96c719b92f21bf8e27b42db"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/overlay.dart", "hash": "b9428aa8930bee03e47536cc9352bbc1"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "bed0fb96275252e2297091fd19c45ee7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "0f351e2163f0c6ea9f3ec5eb8881ea17"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/span_with_context.dart", "hash": "a8f2c6aa382890a1bb34572bd2d264aa"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/inline_span.dart", "hash": "8831386d40ad4cf7660a3266307625e1"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "234f5667a312bcca30a59e788fe46424"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/libflutter_linux_gtk.so", "hash": "4cdedd1069814abfe688ef117ce5f988"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/dispatcher.dart", "hash": "44c4e1de5f30ee41e94e0017dbd5f035"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/sliver.dart", "hash": "5e2ac9d4e8f1c78581cc8aefa3d34c63"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/system_navigator.dart", "hash": "3b98354978b0f9b4903f388399b3b8e0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/grapheme_clusters/constants.dart", "hash": "9f9b79f577d9fdf4f20c17a26a2f1d57"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationdockpattern.dart", "hash": "e05a31b36d602ae06ddd1979c05df7a1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationandcondition.dart", "hash": "698f215aeb2c56fc2970fa91499d8b77"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/isensorcollection.dart", "hash": "b43a69dd26a10426aeb7eed269b4cd51"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/number_symbols_data.dart", "hash": "f176d4d0e0b6d9e454dc1b0f0498507a"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "d9f9f2488723c1e03b8804bbeb41be03"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/plural_rules.dart", "hash": "2241f880365723564463d0bec35a4ba2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/date_builder.dart", "hash": "bc1f35bad7b3fd785bd8734292b27ff7"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/sliver.dart", "hash": "8ace72acd09f9f3961b7f3bec5be3056"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/email_validator-2.1.17/LICENSE", "hash": "f0dcc29aa1e7d4d44e265a0c8b248313"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishellitemimagefactory.dart", "hash": "a966fe9730c6e36f9a0123b9eb1ae505"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "1c43aa902b27d1a8936c77dcf231953b"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/texture.dart", "hash": "888c72929d9b3cd94975f06965e72976"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "65e6240e049c500eeb0bdff33155dfba"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "8ca934b02a5298b8f21a63ed650739b4"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "0c9067d0b8afe6ac1c8b326551773709"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/functions.dart", "hash": "a3aa36a805436731699f39e6bf524087"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationdragpattern.dart", "hash": "2d186bf86fb26df1aca63c78d1f3da0d"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "6f281d35c9453eb6092c1addcb79055e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imodalwindow.dart", "hash": "7837848fa5cbb9801cfadd3856d0479e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtableitempattern.dart", "hash": "d004b4e52622f42ec84eea09ede49f43"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_core_comm_l1_1_2.g.dart", "hash": "7bb75bf1bcc0aac68c67c939cfe2eab0"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/text_button.dart", "hash": "84126bd35d5680f3c48903776fb5162e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/isensor.dart", "hash": "1093e13de26d6c3dd606a01c451762ab"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/dbghelp.g.dart", "hash": "0eab209847ef951bd0a6ff1418f74ba1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispeechwaveformatex.dart", "hash": "919cc78cfaa28ec6b957a771cd0765ed"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/build/flutter_assets/AssetManifest.json", "hash": "2efbb41d7877d10aac9d091f58ccd7b9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/iphlpapi.g.dart", "hash": "2426e2644b69a745c9d477194b9b572e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/eager.dart", "hash": "fe75cb9d73a87bf59cabc3af4d5072cb"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "21cd40fc2ea0defcdc048d54b77722c9"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "00b3d6ec778c057356a8e0f99a4ff588"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/button.dart", "hash": "75c340e47044712f389010dc4a220a3f"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/autofill.dart", "hash": "2c3db13235dd0c924d1367692ec4ae1f"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/card.dart", "hash": "4d2acf9063a0c90341c7af78e293a937"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "76e3e46c95f20cec7bf446ee56306fb8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationselectionitempattern.dart", "hash": "385e7301c1c09d5c45f0531a5c375c6c"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/sliver_group.dart", "hash": "5b3185ef333a9582c4a7bba6185e7ed7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudioclockadjustment.dart", "hash": "d25601f97655927dc9fd147438eacfad"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/form.dart", "hash": "1438fbc7f0f731cf5164a211928a377e"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "67be4bdf31d93f8a5e654ec21d96ed5b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/apple_options.dart", "hash": "d4efda9ec695d776e6e7e0c6e33b6a4b"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/switch.dart", "hash": "7256737065bf49dbff23e83665dfb80e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "hash": "146741f6f87d6612ee7bbf6a6fa9c119"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/choice_chip.dart", "hash": "51f656b4d880a885413a2c963bccfd3a"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "60c553948e23fc8fb2ba2dfb3e7ec153"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/app.dart", "hash": "d9e98b5a81cf86f8c1f595ff861eebe3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/chunked_coding/charcodes.dart", "hash": "a1e4de51bdb32e327bf559008433ab46"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/lookup_boundary.dart", "hash": "0a3c66e5de5f99b50a256aac5e4207e6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtogglepattern.dart", "hash": "b3d8ffb1e79fe86169ef197e01c7c79c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishellfolder.dart", "hash": "a1616e35cb9fc80b351d84aea1626b36"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "e8106b34b25812580ba75dea86a5a096"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/lib/src/messages.g.dart", "hash": "d8a6ceefc2ed13b75c503d01c8911fd6"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "38e1d0509dc1ed42b630c2604c905593"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/platform_views.dart", "hash": "b40780510c9b3d671dd86b07b1f812e9"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/theme.dart", "hash": "3907ade9ce8b9e16265c3ebdff6cc132"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "84a64086b6a56b4fac2100947a29c58d"}, {"path": "/opt/flutter/packages/flutter/lib/src/animation/animations.dart", "hash": "c95cc3d4d3122294945f603ec0b3132a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "20ff58eb86f7132e7b2a18f0442305e6"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "ce40486e207d35a8f247281a34f231b0"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "3bc33c65fa44a57d13430fdedef82bc2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/charcode.dart", "hash": "b2015570257a2a6579f231937e7dea0e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationproxyfactorymapping.dart", "hash": "ce859dde3195c55b2efccee1bdc51a60"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/lib/shared_preferences_android.dart", "hash": "30bffdef523e68fbb858483fd4340392"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/chunked_coding.dart", "hash": "5f5c07df31f7d37780708976065ac8d3"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/clip.dart", "hash": "fdafd11afaf787fce66b7f5890d21241"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/strut_style.dart", "hash": "17c301dab5e5355a9c2683e7f3be7ede"}, {"path": "/opt/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "55380226455ea534ad3f21ab09fa4cae"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl_helpers.dart", "hash": "fac5ee1098b41fef8637aca152781c92"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "d498388a21cc769d98cf664c575d4e04"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/sliver_resizing_header.dart", "hash": "a619d972c76b44bc8d1c3dc88c8445ec"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudiosessioncontrol.dart", "hash": "db4827f3013417baab4977d3f19afb1b"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/decorated_sliver.dart", "hash": "fe0a1ebd99d59b4024033158607723bf"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/build/flutter_assets/fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/data_table.dart", "hash": "59939c42d2baae074e7123d552a36deb"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/typography.dart", "hash": "3589ad5e816918a56c21cafcc6e5a611"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtextrangearray.dart", "hash": "5a8ea03396d41d3b76a510289dee5d25"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_texture_registrar.h", "hash": "455c45888f33294a5054d8adacac5a2e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_core_handle_l1_1_0.g.dart", "hash": "cc8236ed613332ed202cadb26db1c743"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/text_direction.dart", "hash": "45f61fb164130d22fda19cf94978853d"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/sliver_tree.dart", "hash": "5ebb4923442d1fcc7c91f9677e086467"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/tap_and_drag.dart", "hash": "27fc926793fa8892a2acc5b7ebde35d9"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/message_codecs.dart", "hash": "e62c6008d26fdd56ee11d82ca4f1d516"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/io.dart", "hash": "119ed2f372555dcadabe631a960de161"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "c23351f27a693e0330fc77704443a83b"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "6e800790e7858e8e1cdc73c8cc09d719"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/immdevice.dart", "hash": "545e435076682f57181d79d48821ae5d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/user32.g.dart", "hash": "e2d16ea1496afeed39828f05f60debd2"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/.dart_tool/package_config_subset", "hash": "d41965e6673d7bebd635e8c43a255117"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/flutter_linux.h", "hash": "6692e5e4a15db60ea249128fa359243d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local.dart", "hash": "22b26473ffd350c0df39ffb8e1a4ba86"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/extensions/dialogs.dart", "hash": "ca0e62303e3d1154ac7712e05d705c03"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "1f07e42c78eef32e5fa787ecd1e72049"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/constants.dart", "hash": "c7cc72c1e40d30770550bfc16b13ef40"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_datetime_picker_plus-2.2.0/lib/src/date_model.dart", "hash": "4b07ffc8b2e29a1254e4bd4d4bdde365"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/crypt32.g.dart", "hash": "6848c6ac5c6c2b1b40f3dd8ec0bbe31c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/winrt_helpers.dart", "hash": "e2f61b143b6eaca3f6291b32388812df"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "hash": "173ed9cde7e32db3e5ff3867c12b7824"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "e74977ba262a820189b7854350bf9af4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "7e4502c6962965fa58f185d707a72afc"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "dd25c518d50a5334f0a231570f7c919b"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_method_response.h", "hash": "60251ce0dff9106f61b70c6c74b865aa"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "2464c23232ce73eb96d3fba785a62215"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/method_channel_flutter_secure_storage.dart", "hash": "20e7221c12677486628b48b0c30569f8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "/opt/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "6656ba0c69fefef80b8cae101896c029"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scroll_delegate.dart", "hash": "a0740954b44e7627eebd8a66325727c5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationinvokepattern.dart", "hash": "1d7963ea64a6b7059dc1f694f23f0b98"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/colors.dart", "hash": "f3afc3b9324005ca112ccef93598cd89"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/text_span.dart", "hash": "99f994fae7b85fd2e6cfe48e211da58e"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/image_stream.dart", "hash": "facf2204744818c1a3a587be1d7b7645"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_texture_registrar.h", "hash": "455c45888f33294a5054d8adacac5a2e"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/error.dart", "hash": "b831e4cd07f0e2ad701fdf6ac1dafe19"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/utils.dart", "hash": "105813825251a3235085757d723ae97c"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_engine.h", "hash": "1cb444a4e10159155428882dc38a06bc"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "fc74c3e85989d324a76e1a8a2d3f6dea"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_binary_codec.h", "hash": "5997440e26e079541337c498925226f1"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/build/flutter_assets/AssetManifest.bin", "hash": "693635b5258fe5f1cda720cf224f158c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement.dart", "hash": "ce305fb96ca9a74ff549e6ff91795e10"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/lib/src/strings.dart", "hash": "4e96c754178f24bd4f6b2c16e77b3a21"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/material_button.dart", "hash": "da9ecd9bf1968692f391966d2c3c193e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ibindctx.dart", "hash": "3af3fd07f4a1feeb62307f54d5bd0aaf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d235f51d48e43d80a46b35d3ac1a7135"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/material_state.dart", "hash": "3f3f810d0df6a4d8fa696fb14a522199"}, {"path": "/opt/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "ceca8c46e07b211bd755e480b1bd6b32"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/app.dart", "hash": "4a7939e729d46f63553f500041dc7672"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/.dart_tool/flutter_build/dart_plugin_registrant.dart", "hash": "520f81c81ac2396297ea2ba7146c7853"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/.dart_tool/flutter_build/db8f75ad9f718081919257dc8f3b6064/program.dill", "hash": "515745db7e2f8ccc161032a4a5202192"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/src/generated/ascii_glyph_set.dart", "hash": "7050c8c94b55eb51260ca54708b460fa"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "9651186d44281a8caacff14d50734011"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxfile.dart", "hash": "873012eaf19c72c50b8622e17c72106c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/banner.dart", "hash": "0cfa4ee77923dd3607c3e3bf35f3e070"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "29114a10bc26482a660a1114749838f5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/setupapi.g.dart", "hash": "ec1ebd4c36e474539966b09b9d152fd0"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "6d4775307a2bf338997772562d4467cd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtransformpattern2.dart", "hash": "83ddbf5c126feed94b2f90784c17d5b1"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/transitions.dart", "hash": "327867a2784fcc92c5a1737cee7a3197"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/regexp.dart", "hash": "10ca1bc893fd799f18a91afb7640ec26"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iconnectionpoint.dart", "hash": "ed361e60fcf89da03b59c13d84579d0d"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "9537dbc5437603a8b66a7dae56c9875c"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h", "hash": "33df0b32edf5c119fff6fafaa8359145"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/viewport.dart", "hash": "9c0e3742a2b56252c568e7f0a0af8810"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomation.dart", "hash": "e1980812801e0d89e39cfa0bb4cf7fb3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/structs.dart", "hash": "b51cea8017e3cbb294fe3b8066265c7e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispeechobjecttokens.dart", "hash": "2b6a616f4d89d2cc1f4b1004a5e58085"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/adapter.dart", "hash": "0192533b9be6f394b49a75b38f8dc84d"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/date_picker_theme.dart", "hash": "a1ee439640dc3ff94786e7d96122b671"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationannotationpattern.dart", "hash": "2a397f62f7c1670044f38d8f4af1ec92"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/common.dart", "hash": "493b51476fc266d10a636f520fff01fc"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "389764c8c9255efdedf9843e16b3d235"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/lib/messages.g.dart", "hash": "3e127bbafbce223b6d416d5cca517df7"}, {"path": "/opt/flutter/packages/flutter/lib/cupertino.dart", "hash": "e093bedc58f3a92cb4ab66556b4ea9c8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispeechaudioformat.dart", "hash": "36145af4fe8f10df91f98b13659a7b23"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/inetworkconnection.dart", "hash": "51bc9f87faab4993239b12e26047c819"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "2570eaf33e6ce252fa201989b9ee6af8"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_method_call.h", "hash": "96e3a074c3f1f33e88ad3ccc60d6f9d3"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_standard_method_codec.h", "hash": "dd8da806243743ff943e6de062ddaa3d"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "557fc80bee02db6922b6c67f0a40cd34"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/combined_wrappers/combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/string_scanner.dart", "hash": "07758299bbd2261712f35210ee2f645b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_core_comm_l1_1_1.g.dart", "hash": "9c3c2afae62dafae40a282af7f685943"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/score/score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/binding.dart", "hash": "ddb79a905f493ffa11db04d575d1529f"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "aef3722f9d145aea6daf824f7e02a840"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "62c1ce5453fdd075196695637e258b97"}, {"path": "/opt/flutter/packages/flutter/lib/semantics.dart", "hash": "4b784d6e4f290bd6d5a1f38bfb5701d8"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/_capabilities_io.dart", "hash": "faf4d014b3617ede3150f80eba25e3b4"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "92704d1a21b1793d6070f7bee27bfe68"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_view.h", "hash": "c7b190b936829056c464babb02ad0fe5"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "e438b8b77c0b056309e25325952b64f6"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/system_chrome.dart", "hash": "90c8a81d181140ffdcdb8601cdf56207"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "0a1a80151674cfd91279677d9f016bf2"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "db1783b3083765425632b2ca451dbbc8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "ab825e70224c09e49073e60d462f88fc"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/system_channels.dart", "hash": "6147de3bb8f9f335022d631c67c92536"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "e31765e74f6120b5bfdf059885643943"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_dart_project.h", "hash": "33df0b32edf5c119fff6fafaa8359145"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "0f46992262b5e9def5963ee29a6b9881"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomation4.dart", "hash": "beb5454dc4d32af79b6177c6ef646714"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_core_apiquery_l2_1_0.g.dart", "hash": "153449b4c65c20e74489d7853e4ee4ca"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "934a432cbf7baeb2d81ef25a49c36e1f"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/flow.dart", "hash": "79ac8ad87caa659775def3b2860e5a87"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/constants.dart", "hash": "ff053497281f063a2820da0b9ed4d260"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/range_slider.dart", "hash": "bf286075da7a9e92f62095e254609418"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "a4d5362f2c8444701c1e293b85fd003a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/src/generated/glyph_set.dart", "hash": "8a451864f1a46f19700d46fc5d4cbd39"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "8b6832f29637935d19be203efb2b1283"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/filled_button.dart", "hash": "b2cf47ccd5a6cf4843108c3a9f821c55"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/node.dart", "hash": "dff97db228356561674b5f690cd54f41"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "2b9a24c4f3c66c9847e794ddbd1e7249"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_datetime_picker_plus-2.2.0/lib/src/date_format.dart", "hash": "39ccabb7b49ed575f44f5984676b8a90"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "382f7c8ee5e19580005898427950e0b7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtextpattern.dart", "hash": "6e8a57cfea32b9c9f29b229edeacbd6b"}, {"path": "/opt/flutter/packages/flutter/lib/animation.dart", "hash": "29a29ed9169067da757990e05a1476ee"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/libflutter_linux_gtk.so", "hash": "4cdedd1069814abfe688ef117ce5f988"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "4f36e38eaf3608ec18c70c13942510bd"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "9003679f83073733248c1bd73d3097e3"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "d6b57fe12b3df49b484687371c08487d"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "2f21ecaf225265e45135854c47dfed90"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "c970404e32ab9a5917d955f66c830b1e"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "679653d6c84dbbb523d31feca1431de5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/iterable_extensions.dart", "hash": "040a16c5fccfea5a33d4c771c93003c2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/src/utf16.dart", "hash": "07d628617431f09942070c95c65d241f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwbemservices.dart", "hash": "58ebbd139a7de7bef2e2e646cdb00d7e"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/colors.dart", "hash": "bee2e1aabab40248f6e4b4b8bccea509"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/input_chip.dart", "hash": "7397bb1624b672abd4672aaec8334149"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/number_parser.dart", "hash": "b8a405a7e5ea8001bb0ab36de015ac6d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/build/flutter_assets/version.json", "hash": "7498aaed6a0055346eaf584b306ea5ec"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "7e69fcdf387be2ef9513d34ba4bbbbdb"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "70b3c5178a2900b73be78d52770fcd40"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/arc.dart", "hash": "f8aff0b0ae4a957a0e3637de749e41a3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationdroptargetpattern.dart", "hash": "1ea35c2990caf75b07d8a555f3f49191"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iapplicationactivationmanager.dart", "hash": "88d299fd8892c37bab557a1ffb9cec20"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/page_view.dart", "hash": "a8a5b571b1af5fc1c05c3941e3463592"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/gradient.dart", "hash": "831f900bdcad04be13af3338451e99ee"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "d34b1e33e7604b54b656d4c7471ad8a1"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "ec8275100b9b1a1d880b8ddfe8100c9f"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/process_text.dart", "hash": "8c3499889c31838ff4de84d56ebbdebc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_view.h", "hash": "c7b190b936829056c464babb02ad0fe5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_directory.dart", "hash": "62da8696885bd25977675ac4f7f1aef9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/grapheme_clusters/table.dart", "hash": "29e1858c5ebc2b4dc6d1528196bfb1b6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_link.dart", "hash": "600a83d8e8dcbc1fde99887eea16f18e"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "hash": "f27209609f9689165f058b3ca18165d9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/bthprops.g.dart", "hash": "791b58d08f6e26165658bbd5ad0c5b2e"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "4af4d6cbc2123755fc589f044bac8d6c"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_message_codec.h", "hash": "44b9ac9a008150255bb2e40ad145b47c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/method_channel_shared_preferences.dart", "hash": "513d6195384503beeb7f3750e426f7bb"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "e7fa4c3593bdfbc187766bd200e60599"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/src/eager_span_scanner.dart", "hash": "b71ae933bdc407aa375dba5f378198a4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienumnetworkconnections.dart", "hash": "ee244b933f07447928851d56170a8050"}, {"path": "/opt/flutter/packages/flutter/lib/src/physics/simulation.dart", "hash": "c0fe6462e3a08d6d6afbf4f66130d494"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwbemcontext.dart", "hash": "659397ba2b8ba2809c7855a21f2f60b2"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "11b4d96c7383b017773d65cb2843d887"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "66650a747992c52961dad0ac63628efe"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/platform.dart", "hash": "e654c239899a91dab05b11cf0121129f"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "d37e33aaef71722417cb64537e97092d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/selection_area.dart", "hash": "393af2615234850ced0bb056b30fd133"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "8ebc4ef8486c9875330658ed1a145020"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestpackagedependency.dart", "hash": "793424ed524885eedef0340c067b865e"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "347ca56667b68d9d66174f8200b4505e"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/material.dart", "hash": "ac787747162d980c88c97f09723f31b9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/router.dart", "hash": "b294af2c4f13d55aba2bf44369a7ace3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxfactory.dart", "hash": "aa34ef78c82b66e4c309bd5f4973e3c0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienumidlist.dart", "hash": "043bb1fa01132048a01458c6977636f5"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/debug.dart", "hash": "34df6a85c7662f602e66bba150c4fab4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/restoration.dart", "hash": "ee984ad6a59ef4e7fcf5caa40736878c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iprovideclassinfo.dart", "hash": "c90759e0e90f88fd2b4f177ec55cb4f4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/bidi_formatter.dart", "hash": "5c81dd07124ccc849c310595d9cfe5be"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/flex.dart", "hash": "1e20ba56045ec479eee76a7a78127684"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/binding.dart", "hash": "6c5adddb22296bd8e3a6b50a3a0ffbbe"}, {"path": "/opt/flutter/packages/flutter/lib/src/scheduler/binding.dart", "hash": "594fc6041eebf4706476277d9d12e659"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/heroes.dart", "hash": "ef07fd7a67d6cd5ac37c5c20863af323"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "cf064f126091733539cece8deeefa1d0"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/search_bar_theme.dart", "hash": "5c0db57261dd10e5f760ac757f4fcd76"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/pubspec.yaml", "hash": "a3146096e3dac5bf91af40fc86ff1fd6"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/clipboard.dart", "hash": "2a64735d53a1dd225670c23206f09e60"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispellcheckerchangedeventhandler.dart", "hash": "e82d109f954c4a736896b202eba01ce1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system_entity.dart", "hash": "04e7480fb89755fcc5f64f7d80ca610f"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_method_codec.h", "hash": "ea928ca76b491e0a1313d69a25103ee3"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "45621326af8ac20233a5cf51cb1f05ce"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/istream.dart", "hash": "752db229137baa4ff1a3eccbe3cf69b8"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "3f80a1a01d62cd07e1ae5d34f9547b69"}, {"path": "/opt/flutter/packages/flutter/lib/src/semantics/debug.dart", "hash": "3fd33becc9141d8a690c4205c72c5d40"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "70b3e472309bc151209b07238c849c38"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_response.h", "hash": "60251ce0dff9106f61b70c6c74b865aa"}, {"path": "/opt/flutter/packages/flutter/LICENSE", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/extensions/int_to_hexstring.dart", "hash": "73cb6deeb88fdcc320cf8e089d51531d"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "9590cac03fb5c0de43845ee528380b4e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "/opt/flutter/packages/flutter/lib/src/animation/tween.dart", "hash": "1a0a7a32ca4a38d882836e00d31cd8b7"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "c8bb12d41ce48bb27184ddd257d2859b"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/icon_button.dart", "hash": "946edf55d16a6affc23769f4e73e5fee"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/lib/src/shared_preferences_android.dart", "hash": "6f05b68df1b893e73008d1831589377c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/arena.dart", "hash": "b3dd803ac13defc89af5ad811ea2ca31"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "5a00bebaee1a7921574914f7dbdfff66"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "fa4de83c5c418c49affda2b164993f99"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file.dart", "hash": "1026f587763defb6fb1eec88c2154a3d"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/image.dart", "hash": "9559118068f6ba981881a07626ef2d3f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/src/allocation.dart", "hash": "7c8e196c6c96efaadb0e605ec3cb1cce"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_texture_gl.h", "hash": "2ff91c9748bb7c022a2aeee861f51b81"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/temperature/temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/circle_border.dart", "hash": "80df17b3f631d4ab4bd83a5ccb76a875"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "c1ee64b6c171e90ac92885883f0f9ce4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement2.dart", "hash": "a67676334dcb7629a485b52714780808"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/shlwapi.g.dart", "hash": "bd016bc06a43b71c304daef7333df5cf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/constants_nodoc.dart", "hash": "1db40035d1ebf40cde9cd2a90c1a83e8"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "32d8ff829d8956046c0a91c8ae4160a2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/inetworklistmanagerevents.dart", "hash": "a403f9be5cc42dedca5208fa2c104dd3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_async_platform_interface.dart", "hash": "03664e80d73ff10d5787d9a828c87313"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "44fe04a4d23a1e0df1470c5c0eacfddf"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/title.dart", "hash": "dee4f18e2804e238c57a305ccd28eb85"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/style/windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/types.dart", "hash": "4a1d1bdbd4e9be4c8af1a6c656730a66"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispeechvoice.dart", "hash": "e4db97d8d7acb9a9585f38b0df246277"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/checkbox.dart", "hash": "********************************"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "78fbd8387fe15451421bbe059e3deeb8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/messages.g.dart", "hash": "1567572a579e5f2aab31966d4a056855"}, {"path": "/opt/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "7ba47d8d884d71e42aeabcf62344873c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/web_options.dart", "hash": "7dff3a0a1b5652f08f2267907c79844e"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "493bf1de83d600106fe27cb741bd2012"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/extensions/unpack_utf16.dart", "hash": "cfab296797450689ec04e7984e7d80e3"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/elevated_button.dart", "hash": "05ae77852a25316df035464cb7ff2509"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtablepattern.dart", "hash": "f0583593722d8dbc8d76df7f7df11dc7"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "820396e431a6e00298858ae0931f50a7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationvirtualizeditempattern.dart", "hash": "9583f92189dde339b1884f57e7b2f9b0"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "b32a871f85e9be997f09ce9bc7a7df32"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/lib/clock.dart", "hash": "2c91507ecca892cf65c6eaf3fbe0a7e6"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "d64508f06238e79c4c99b1af662963f5"}, {"path": "/opt/flutter/packages/flutter/lib/src/animation/animation.dart", "hash": "efb0eae05f940ace69b5be93759acd1a"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "c9391709a58b30c3f3812cc2b5e249d4"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/view.dart", "hash": "c04dc7f29cc9a9696d55c176099bcce6"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/lib/services/api_service.dart", "hash": "62c3f9dc727805b91014115f1ff1ac3c"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "660924f2468f228b5a32596ae5bb66c5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/propsys.g.dart", "hash": "769b47b5febf91e7831fd0040b4d3ed0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/version.g.dart", "hash": "b967c8105d10206324262df9fb1a662b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iconnectionpointcontainer.dart", "hash": "83f156972f99a181b244f428cdf134bb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/lib/path_provider_android.dart", "hash": "eb368258f0f9fe56110bdc238488af97"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "75c38766ddb6a4505dc9271c6a9fec49"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/drag.dart", "hash": "d5cd032ed35d1180819dac9428cd1035"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/time.dart", "hash": "71462b0c828c47fad9b2ef1ef68cd9d1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_datetime_picker_plus-2.2.0/LICENSE", "hash": "25bdf20ee5ab3c8348e5f77af49af787"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/inline.dart", "hash": "7f107258e4c6ceef750c5b59f287068f"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/actions.dart", "hash": "eb295ba3319c0f2b1c910d981594bab7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/chunked_coding/encoder.dart", "hash": "dbf4f1e95289bc83e42f6b35d9f19ebe"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "366f1ebf48ef3c69b4e7a9ddcaa8f3ca"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "5528b93def00b5b750c964a10f323900"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/async.dart", "hash": "642ff6f56b746c990445d42331e8ff81"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "c6cd5ec6babebe0af7224db7ef8fd680"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/oval_border.dart", "hash": "9ca011fc6acdcd04949fc9c6ec849043"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/curves.dart", "hash": "02be76cbb0e6152f7162d22383971f51"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/page.dart", "hash": "4ed1ea1e77b6220b80b81d0f65976c73"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/binding.dart", "hash": "be313780ab3939da8cf5e43dd81e8ba8"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "449d44061d435c8bbc5366d6eacf065a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "8965e1b5627c77d3978ae3d08e32f982"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "eca62c60db96d71f3cae9b506875c03a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/response.dart", "hash": "efbedb75be354b65520bce3f0855b8db"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/linux_options.dart", "hash": "26c4f0c369b83e53900ac87bf7e0dcff"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/lib/services/token_service.dart", "hash": "2d82a7a52ca520b5e7fc0b2c783cccd7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iinitializewithwindow.dart", "hash": "0748bf03bcf37edd1d571959e45a5cc0"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/routes.dart", "hash": "b31c6998027d8e37855bdc3be9702803"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file.dart", "hash": "3e30d0b7847f22c4b3674358052de8b5"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/motion.dart", "hash": "374f899d15352be34ce61fd5243bed08"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "bad351fb6af0e461426ed48dc007c6f2"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/lib/models/user.dart", "hash": "89576e1ca4a94b6b66ac1f5eb0a41037"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/collection.dart", "hash": "476383869aff7b87579a7753e47722d7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/lib/src/shared_preferences_async_android.dart", "hash": "5cfe2d9d61584eae2e9c8e81be1dd9c5"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/action_icons_theme.dart", "hash": "3fd3c4bcbbf54fbcad3b048e3c89d43f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/characters_impl.dart", "hash": "3bb0652e163327c58784ce2a2b882a7c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationstylespattern.dart", "hash": "7326647ec0ab13c912ff9965ccfb4081"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/visibility.dart", "hash": "043377dddf07af1face4788c64ab583f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxfilesenumerator.dart", "hash": "ffc5c2e273fa5a533521f5e67f6e183f"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "0eef32ab9b2cf423c48e89f2dcd9bd6b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationproxyfactoryentry.dart", "hash": "34f1383424d8e23bc3463188bcf19dcc"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/lib/screens/home_screen.dart", "hash": "a044b8fc4d288659aa28ec6577265d71"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/utils.dart", "hash": "caf148b76c44a3f0f1bd6055ddbb8f5e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h", "hash": "d18aca92fddd9924e6bdefb1bb7c9a00"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/isupporterrorinfo.dart", "hash": "0318359df96d8b438340156129fd1c68"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/force_press.dart", "hash": "bd21408997d44d0bd83cf6d38bf3d2a2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ifiledialog2.dart", "hash": "f45b881803064da6852bd34e8ef7951c"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "hash": "170fe4655f45b54388ab850399d92895"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/inetworklistmanager.dart", "hash": "e165be390861acd35be3189fe414b105"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_texture_gl.h", "hash": "2ff91c9748bb7c022a2aeee861f51b81"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationorcondition.dart", "hash": "037c1b4cc41d0a66ea6134bf054ac095"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/email_validator-2.1.17/lib/email_validator.dart", "hash": "695dc653ee6274c6e7a6ddd14fb7d27d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/comparators.dart", "hash": "d1410f48ac374235aaad55cba40bc4be"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/winmm.g.dart", "hash": "b670f26b5ebe125326b4ceadf218d1fe"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "438d55cb1016f68c4db0da87b19ac82f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/path_provider.dart", "hash": "e08429988b4639fb29cd66bfdc497d90"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imetadatadispenser.dart", "hash": "e653273473a891c0739e255d1b469d55"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "14e0c453a6ee4b91f65cab141d997a88"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwbemclassobject.dart", "hash": "fa0457adc89723d08bb20eddf3e89555"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/structs.g.dart", "hash": "67f751cf689639227d5db57f73b92a2a"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "e0da92f4435a19c3c666cb8f88cf4502"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "203611b95394d042fae8bef59a4ebe40"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "479493da08b4e2137fc162ff23bef99b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudiosessionmanager.dart", "hash": "487d0d91f9dc55efcbc2a686bbf46b8d"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/lib/screens/login_screen.dart", "hash": "3228b5da98e64a779ba52f796e5fcb2b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/directory.dart", "hash": "8f4de032f1e2670ca51ce330a4de91a3"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "faf51c4fe1dc7af7fabc7c78a960305c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishellitem2.dart", "hash": "b0c96b9383b0471bcadb2206daedef05"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudioclient.dart", "hash": "e3cf86a21b6646a68ce37d952b5ecf5c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ierrorinfo.dart", "hash": "aeb565e28b1e55ec3794a6b88d975aa5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/style/posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "0b0f625bca76693cdeaa1f4358809351"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iknownfolder.dart", "hash": "9805639600096c1f056657f418f6703d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/wtsapi32.g.dart", "hash": "d6db794e2f414caa48650e6bc2396e6d"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "f9d2ca632cbf0232de1a3b8826d4286f"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_value.h", "hash": "92d2815c8f66f5196925998f9d4828a5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtreewalker.dart", "hash": "865471d167a94c3a9bad6cea64f10834"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwbemrefresher.dart", "hash": "54ba07d769f852b6c68fa2aafd4257c8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/micro_money.dart", "hash": "391b7eda9bffdd4386292eae157d449c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/LICENSE", "hash": "aca2926dd73b3e20037d949c2c374da2"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "e7d84c68f69f7f105e4acca6946ded83"}, {"path": "/opt/flutter/packages/flutter/lib/src/scheduler/priority.dart", "hash": "ac172606bd706d958c4fe83218c60125"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/itypeinfo.dart", "hash": "2fe7a01e6cccd3fc371fd2d730935afe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/kernel32.g.dart", "hash": "edf7bceb5006082ec684ee177cdf3025"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwbemobjectaccess.dart", "hash": "cb5493b3fb9ca309e2cae9a641029cd0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement3.dart", "hash": "e0417e8f067bf4a25edc299853bfe050"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/button_bar.dart", "hash": "99f17d3841e146c27a0079f86b453123"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/search.dart", "hash": "51d9fc6dac581a413a064529ebca2aeb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "9b9a244b67b67b84170d3c3ee4ec2c73"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "d28e7661d865b8f173e8d016a845d4fb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/extensions/list_to_blob.dart", "hash": "56d7144236503f311a7d9a966eaf2fbd"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "bd943d36cd0bfe732ff0bcad174fbab7"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "f5b4267f1c1f72ab634a2be53517d1a1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement9.dart", "hash": "7339ec709c898b8e442a3a02e63f3e6f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationcustomnavigationpattern.dart", "hash": "f71a5e0c2e702bd1f70b7f60ac19eec3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ifiledialogcustomize.dart", "hash": "2815892e3735c223c62476ddaf4cb27f"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/tab_controller.dart", "hash": "e2ecb5f050ac73e6ea80ccc06cb9d7d3"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "0a90eefcfcb46a3f293d18d1142328ec"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/serialization.dart", "hash": "41bd294b2c2eb1b089ab65341e92fd83"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "d0495513468d137435fad7178ad39b4f"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/widget_state.dart", "hash": "91f052d253200c68fd039b858db9d7ec"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/windows_options.dart", "hash": "b4355b7f9f9e50017ce52a8bda654dd1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelementarray.dart", "hash": "41baecfe75bc82e8dae966eba92c23b7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/immnotificationclient.dart", "hash": "1cf0553fea22eee05a0cbb29e299760a"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "1b0a0fa63fc381a5f245af8f5d424f2b"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "d012d47412ff8c4fe1cbe6ac1d53b333"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishelllinkdatalist.dart", "hash": "68642e049d1aa7d3e55fc1382b0696c0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/flutter_secure_storage.dart", "hash": "5a944801c9b2bd3447f982168b31e46c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/view.dart", "hash": "9113446eba03f06e109fba1b295bd26c"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_call.h", "hash": "96e3a074c3f1f33e88ad3ccc60d6f9d3"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h", "hash": "3d850b5432140f7e565baa806ee1f716"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/star_border.dart", "hash": "d51d434283a0193da462cab610325483"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/theme_data.dart", "hash": "69e6b2a0e521311c4d86f8d7e93e29ed"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/dialog.dart", "hash": "b066cb536dada4801466a198ed505a8d"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "fb7de9357b27176361434afe3ef378c4"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "86ba004de80b95197e3dbcab1233743b"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/deferred_component.dart", "hash": "f7b634b150a8381c9b4c03482a0d6e6d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "ad6bf1d7b3079f5be69fb40ada4fc145"}, {"path": "/opt/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "e8f704ef18663c53424e306c38d97d39"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "eb2a941e76ef3aaf9ff856a5d93e0f7e"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "13be7153ef162d162d922f19eb99f341"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/scaffold.dart", "hash": "4e8c9de420d0f15c8d396497675bfeb3"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/multitap.dart", "hash": "87fd57b3136ca33a1e97c5524b74e112"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "8effe6176ace6ada9ad1db0370cf2e78"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/characters.dart", "hash": "21bf6725b1fc374f03ae5b2cb46bd95b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "25e50c0fbe710a5f7a590c3c26703f60"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ipersist.dart", "hash": "98911449216f1b1c1b092954bd6cebc5"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/lib/screens/register_screen.dart", "hash": "2a584272a17d935cc299716df9395f66"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h", "hash": "c9ddf466fd4c06d0991614cd26dd74c5"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/about.dart", "hash": "3b10ed50f64576ade40f060080b86ff5"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h", "hash": "dd8da806243743ff943e6de062ddaa3d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/client.dart", "hash": "b16458199371a46aeb93979e747962a3"}, {"path": "/opt/flutter/packages/flutter_tools/lib/src/build_system/targets/linux.dart", "hash": "7f9c3973e0121ac173a296af8cbfe365"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/isensormanager.dart", "hash": "bf3a7e591cc9c80a09c1843209bdafdf"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "7f7ca3170e520952778ebe749d3cef68"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/gdi32.g.dart", "hash": "3c738a2ffff7c2ec664bdd76408deff9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "0491e1cca60da329c2e03c48abde07c9"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "86b06851f3ff5ee17bb39fd0d241cbb9"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "ada618567778b8fea2e8b455e9100a07"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/link.dart", "hash": "1f334b50f4df781bbbfab857581c3540"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_plugin_registrar.h", "hash": "3d850b5432140f7e565baa806ee1f716"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/button_style.dart", "hash": "43a371c8c7e417fb0a445ee20360d434"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iinspectable.dart", "hash": "3fd143ba1c7f9f9098563ee5b342b240"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienummoniker.dart", "hash": "d80a4e0d1a5fe4aba72f8df70e8b660d"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/text.dart", "hash": "1bb32014080277421d4d679148d11bb0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/scrollbar.dart", "hash": "a6c5b8639baab209a7d53dc7e0d1cd1d"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "aca21d4a3c0075a4d0109e79e87d7f21"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "195ce88bb0f906dd500f3ee23c097a95"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/ios_options.dart", "hash": "704d7f872888ec6e9697123a180fd95d"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_method_channel.h", "hash": "5a5b80bcf4d72767c5fee74693a1140e"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/tooltip.dart", "hash": "0a7ba6f4900ffac5152d0a67876d6016"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/button_style_button.dart", "hash": "e08b76d444c9753d483bf6975ce61fe3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/advapi32.g.dart", "hash": "e88da16e1dcd78ac58401bf0c3134c89"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/action_buttons.dart", "hash": "7aa7a3f6fe7120292c1dd2eeb95eda39"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/compact_number_format.dart", "hash": "9068f4d63af1ec44245b76b7ab4dfa48"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "1b59b4ab9530ab7549ca3c53173f4814"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/expand_icon.dart", "hash": "74ba66d118a95cad3da88b944da7a9dc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/irestrictederrorinfo.dart", "hash": "6bca90e19560bd62e32b8e41c835d71d"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/basic.dart", "hash": "f3a8fcfcd79b619cc886547b8ff9b25c"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/segmented_button.dart", "hash": "1f9d085b26323e03c82077418ccc0a4d"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "86a6fc84462d9d59a64d1c32494e96a6"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/selectable_text.dart", "hash": "2dc7dd4788151c9148ad7b2a91058edf"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "2df422a56d9988b696a9b0950f28bee3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iagileobject.dart", "hash": "4bc403cec1c5846051bca88edb712a8c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/shared_preferences_linux.dart", "hash": "492280af61b4bca29e21d28db0c2be1c"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "fdbf119306639c731a4d90a5f56cee7c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_1.g.dart", "hash": "902f4e12222d1e5b34d0ec77b2628f25"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "b2516cc7704e0c10a5f1d777ac857ea6"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "5b284216cdaff11bd25b0175739bf48b"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "540497224c553a9b08b20397bd78ef69"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "6d3415786ad91086c9ad871e33c72e31"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "f183c429d3db89b9c97dfacaa85f09c3"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/binding.dart", "hash": "9fa617fc06f3cc28a784af28fdfaf536"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/spell_check.dart", "hash": "ece5ee01a742a658ea5b2ee2a328c418"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/image_provider.dart", "hash": "b7a56c40e458c896ddfcb63f1b7aad62"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/spacer.dart", "hash": "31caf5d9d4f0d5e2b373a2bf368290d6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/immendpoint.dart", "hash": "08f987c2f95b3e2a51c435bd8e8c588f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/blend/blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/immdeviceenumerator.dart", "hash": "f31bb216ea8990a64c2326c16fd2ea33"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienumstring.dart", "hash": "e7c7233769f55e718ce22082f70db721"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/table.dart", "hash": "aa6152a8dc858cd16cf240ebfa31d605"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/tweens.dart", "hash": "959489b18fda284c434701586b43c66b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispellcheckerfactory.dart", "hash": "419b1d6dad30c44e241a804453f78d56"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/action_chip.dart", "hash": "aee97b3fb21f5d87cc7a4c1079c77aa4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispellchecker2.dart", "hash": "60ed6e3dc269f179875fea840112bc4c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/comdlg32.g.dart", "hash": "9821568488904c8c1566c46762278f16"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h", "hash": "b550bdc7d3190030715e0fe3b4b7f014"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "c8266c3435b50929eb834df245aa2544"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/byte_stream.dart", "hash": "c02d47d7f7e95654d3eb9b795e416dda"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "40c9adb59d6f9b10d60bbebbd42d57d8"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "248365a073cb187f63e1c3dc8ece112d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestpackagedependenciesenumerator.dart", "hash": "c81b77e6c86772f05b86739d8ba68b14"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/image_cache.dart", "hash": "11c860591b76d1994f12042408831c94"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "69da3c88c748658e0c9e7216fd9398cb"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/system_sound.dart", "hash": "2dd7e3b55dc8a0ddfeee22f0119b0082"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "fd48427e65c5910cbba1fc3e4e57cfcb"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_value.h", "hash": "92d2815c8f66f5196925998f9d4828a5"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h", "hash": "6cba49677d30f16cadf943f2648123fc"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "280f78984a3d21c2b797d427c12b4c4e"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "5a625dd07fb7a6dc55fa0f5c063a2f91"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/src/relative_span_scanner.dart", "hash": "142ba8c5793aa338f44b464329dd0699"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system_entity.dart", "hash": "22f170a8dc9abfac2942555e83589e1a"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/time_picker.dart", "hash": "70897dd932d388476b6a9a96fe349e25"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudioclientduckingcontrol.dart", "hash": "54a357c7c827b2616fd5e9ff6fccbfd7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/chunked_coding/decoder.dart", "hash": "dbff400b121e6f844298946531d490a3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/src/flutter_secure_storage_windows_ffi.dart", "hash": "2ca4b9e92c39403717d2dcc32bed57e3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_platform_interface.dart", "hash": "59bb1cba1648db956dccb835713d77d8"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/autocomplete.dart", "hash": "67795683a1a7b3dac0854a1e5da146ac"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudioclient2.dart", "hash": "47b806a0c94783b8af1876a42cb6d0cb"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "8635fbec89c2cc03404a2a3233d31bbc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/isimpleaudiovolume.dart", "hash": "654b609384b7b69890219a8d8eb510ce"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/android_options.dart", "hash": "2d04b343ac3e272959ffa40b7b9d782c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_datetime_picker_plus-2.2.0/lib/src/datetime_picker_theme.dart", "hash": "a26fcc765dc456e8c8f5da80c84c00eb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/term_glyph.dart", "hash": "1adcc56e3affffb23739c7c9d8a5fca0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/empty_unmodifiable_set.dart", "hash": "d2e49f7a3cc02c7bd120dd5e4b9daa33"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/date_format.dart", "hash": "20dc50b53035a8e953b5d4ffe6948ede"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/constants.dart", "hash": "195aceb9dfe0dacbf39711b8622ce2b4"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "hash": "61af6ead2e2dc04677bcfb8c0c2104ab"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/scan.dart", "hash": "9ce6595770687511a1c77ace6f55bddc"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "24cdd2cb365ef36394210a26c9fb1dda"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imetadatatables2.dart", "hash": "bb9a6b8b9225608821735003ffdc8a5e"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "2c45e8b9705774232ea4fe1cf5c47b30"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/.dart_tool/flutter_build/db8f75ad9f718081919257dc8f3b6064/app.dill", "hash": "515745db7e2f8ccc161032a4a5202192"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/converter.dart", "hash": "22fdfe2139eaf6759fc40d9fa5fafab4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishelllinkdual.dart", "hash": "2e8ac7faef1638b9d8022b3da82c3588"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/testing/fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement4.dart", "hash": "a212841ba1b80a845ce3756241645d58"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "2f92c28411483032fb7c0a851ebbbb5a"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "d1888a7dc676f48f137edbea755dd324"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "610943df3ed3669788eee55726cb7035"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/banner.dart", "hash": "7ade1fb93c1507812cb7e1f3023b3cd4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/wrappers.dart", "hash": "91e47ed79ad65391642894923c520b26"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "629b25992db59361e5889a42c7de583c"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "bd34896b1432d6f707498d3df7a7c3ae"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestreader.dart", "hash": "bcd1f230f11ee46b0ed40d340d9591c9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxpackagereader.dart", "hash": "2c9b99820a7ba58eea5e30ca3585c24f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/dwmapi.g.dart", "hash": "607cef6a651964e0339d2e09df046c09"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ivirtualdesktopmanager.dart", "hash": "83c5918696d44ca1be713318a4f5a6db"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/build/flutter_assets/kernel_blob.bin", "hash": "515745db7e2f8ccc161032a4a5202192"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_texture.h", "hash": "d1894650bbfe9ba65c6f0b195645a9a5"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "3ec0013bd7ba2e0f89cb963f867f0d96"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/flutter_logo.dart", "hash": "32187ab06a29c3f5929b9f26fd5ccb8b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtexteditpattern.dart", "hash": "7b53b9344345e99b1ec1c1e6247b0f78"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwbemconfigurerefresher.dart", "hash": "24c932dcdfa3c21be567bbe9dd305845"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/snack_bar.dart", "hash": "0127a67d8f96461d1bf6986dcb6260ee"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/viewport.dart", "hash": "e797d0f85b6b031854f48a68e6d9f9de"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/menu_theme.dart", "hash": "887a4888dd10dc19020553757a12bf31"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/constants_metadata.dart", "hash": "4d74fe38db99d0b2b135f8d4f81d6721"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/text_theme.dart", "hash": "666d237daabc113b3ba78fa363d47017"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imetadataimport.dart", "hash": "b8252455a884dfc13966cec360c9844d"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/decoration.dart", "hash": "975c2fb9e84493af10e44456cad39bc4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtextchildpattern.dart", "hash": "002fd240f385a66281c63dea9b31c069"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/card_theme.dart", "hash": "46ffe5265ab96981a4304879f9999d5d"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/outlined_button.dart", "hash": "8ac1b57ab29335fd50ea76229944a074"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomation6.dart", "hash": "3a7c0d6ff07fca442df7724d853dfbe0"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "62877c9dc6386e2c18c23df696e12753"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/licenses.dart", "hash": "365a4e930595816e20608286252762dd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestapplicationsenumerator.dart", "hash": "8d4c4f339184d3cd86b0dfb7d7321d51"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/dxva2.g.dart", "hash": "73ec60b4a67001fb2adfab990c932c6e"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "51bd3df911d2f628879de56dcf93d74e"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h", "hash": "5edcd4ae8b7d3acb2e56bf8bfad75d4d"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/radio.dart", "hash": "79c4abb58f097365e2627de32331bffe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/border_radius.dart", "hash": "86e192e4762ca754c4af48f3fea22501"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/text_scaler.dart", "hash": "fdb8e71a63736d013545f8830dc5c495"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "a26d02dca4465f31f1b4ff143b615052"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "7ffc0b0f4fae5d61fd47de2d122548eb"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "33ef56c795b9e96d640165cecb2f5032"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/build/flutter_assets/FontManifest.json", "hash": "dc3d03800ccca4601324923c0b1d6d57"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestreader7.dart", "hash": "f697b51a3a96ab52efa2c082f20a738a"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h", "hash": "ea928ca76b491e0a1313d69a25103ee3"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/table_border.dart", "hash": "718652a205e0c90b9df973c701c36a02"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/constants.dart", "hash": "0cb06ef1fbbec09f85b6b40cdeaa2f9a"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "0eae8cad9d933f0478d8387400def317"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationlegacyiaccessiblepattern.dart", "hash": "147fdd9503161f6606b625f0ed5c1272"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/app_bar.dart", "hash": "********************************"}, {"path": "/opt/flutter/packages/flutter/lib/src/scheduler/debug.dart", "hash": "0b9b4bed20850a8e57b54642782a88b2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationvaluepattern.dart", "hash": "ede54fd11e6d44588748f07a8711f863"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "/opt/flutter/packages/flutter/lib/src/animation/animation_style.dart", "hash": "d91b7655dec73b3db3a82040be7410f4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/enums.g.dart", "hash": "68763b18d67fc053a444301a9deffb33"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h", "hash": "9093dc992b7f0c62c899bb5ee75437bb"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "1dc7dcdd70674a9f80245280f277e7ff"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/isensordatareport.dart", "hash": "50a6a93f5f53543a005e436586f9e24b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "537e9b325c6baa5e02649883f515cb86"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/src/arena.dart", "hash": "b9bf4c34257ba7cad08d8c7092c46e35"}, {"path": "/opt/flutter/packages/flutter/lib/foundation.dart", "hash": "55fdc7e9835582d898780b018eaf85d5"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "01f79859a59693addf84974953e7f9c2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/ole32.g.dart", "hash": "c1527bbe7fe6973a697108d13c3da85b"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/text_formatter.dart", "hash": "e470881e8e68d9842928c6fa1a889596"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "7fc0ae9c724cfdffdda5de63fa6d0972"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "e66727f5e99e5f6a3d58e1bf0c47aa97"}, {"path": "/opt/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "a753413d3971339169c4a103d7ee3f6a"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "03663b6e95c127f3c83bb84ed2994bb2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispellchecker.dart", "hash": "b868a7ab9e1be413c489dc9958bf907b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/oleaut32.g.dart", "hash": "fae27a92131d4f2f2005c5312e499b8f"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h", "hash": "5997440e26e079541337c498925226f1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/combined_wrappers/combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "5b273bbab831a700c166cfa57a2e089f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/global_state.dart", "hash": "dc4e3bf96e9c6e94879d54eaa2f81c69"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/flutter_linux.h", "hash": "6692e5e4a15db60ea249128fa359243d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationgridpattern.dart", "hash": "142eee94af4418beb50a22e4c3970309"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/style/url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/icon.dart", "hash": "20fbf0ae1f42909e7806add12b2c6e3d"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "496982c4b90528a5360d8064ddd1373d"}, {"path": "/opt/flutter/packages/flutter/lib/src/physics/utils.dart", "hash": "727e4f662a828d4611c731f330a3d79a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file.dart", "hash": "58edba46526a108c44da7a0d3ef3a6aa"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/badge_theme.dart", "hash": "f179cf16ea560111839fc980420e3b18"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispeventsource.dart", "hash": "761edf39926ba43b2d6c95d677bad6ab"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/service_extensions.dart", "hash": "b5d6c349fa0259f1516951989e4d5bbc"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/table.dart", "hash": "46d984bdb7a861219c238974d6bbade4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_plugin_registry.h", "hash": "9093dc992b7f0c62c899bb5ee75437bb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/src/utils.dart", "hash": "fab8d6d1b0e81315a3d78131394d31e6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes_dart_io.dart", "hash": "9df03a340058a4e7792cd68745a4320c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/characters.dart", "hash": "188d03c92376ce139ce247b0f9b0946e"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "f76941994ddf30e398313421f1588d85"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/.dart_tool/flutter_build/db8f75ad9f718081919257dc8f3b6064/native_assets.dill", "hash": "d41d8cd98f00b204e9800998ecf8427e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationcondition.dart", "hash": "0469c2fefb6084f264cd0df8bce7263a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestospackagedependency.dart", "hash": "fd0e866e44796643d6fad18400ea6a77"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "cee61ff4bc1494858ec39f8c4f09c1a6"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "eca4f0ff81b2d3a801b6c61d80bc211c"}, {"path": "/opt/flutter/packages/flutter/lib/rendering.dart", "hash": "72c648384f9af54c9963265d96c87278"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "cb19324d7400b29cab877e6fd6aa0289"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienumresources.dart", "hash": "08a61adc8ecc7216c84a455539fd75ad"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "006f80560d9594a875c5218518a765a9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/rometadata.g.dart", "hash": "cad4664b0591b1060e3eb77fc3cfdfd9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/bluetoothapis.g.dart", "hash": "eeeb5875589f6bf64f72c094f0435b92"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iknownfoldermanager.dart", "hash": "48d51a5672af342c6b1376d1ff04a4a5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imetadatatables.dart", "hash": "fbce92f0e78e457538005bcb0b9a79f6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/bidi.dart", "hash": "68634d4df864077f507d84d92953a99b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudioclient3.dart", "hash": "025a4f4e26446bf3da88ee7d3cf3d0f2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.2/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/isequentialstream.dart", "hash": "b59195eae40d21212bb7b532313e6480"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/base_response.dart", "hash": "ae42d99121b00899d038edc753e0b23c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/messages.g.dart", "hash": "3f45d05cfb9a45bf524af2fa9e8fb6e6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/route.dart", "hash": "bd7e1a7092d5b455dcd4ba18801ffb5f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/input_border.dart", "hash": "ffe5391f3b53b542cdd817dcd85e2638"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "34ec8e649166b192586b754ce67094da"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes.dart", "hash": "3e82e75a5b4bf22939d1937d2195a16e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationspreadsheetpattern.dart", "hash": "c46a3b47927574d4a8ab22690e395c2e"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "f4f97e64864383af2f259063e32bcf49"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/binding.dart", "hash": "52e418c1649b02d8f12083c6ece3f93c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/date_format_internal.dart", "hash": "46f06f2d32f61a3ebc7393f1ae97df27"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/flutter_secure_storage_platform_interface.dart", "hash": "8dac3815609f98dfefa968bc2ea4a408"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/autofill.dart", "hash": "ab99fd7503e96bfe98caf7abf357e186"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "7a582e4809f3b12a8eeb2aeafd69fa61"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/toggleable.dart", "hash": "df738d0b6e09c730283cc61d14ce2ded"}, {"path": "/opt/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "d97019cfa3be6371779fc0e65f2bc118"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/live_text.dart", "hash": "7da554c3a69a1c2d019202e3f63331c5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ifileisinuse.dart", "hash": "5abf40e886af8feb42ccc62d31044f48"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "8c3714021359cb875206d486f25ee2c9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/xinput1_4.g.dart", "hash": "110291e1a5dee5de6d06425145c9f53c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/_bitfield_io.dart", "hash": "64b62181e86fc993a65f92bdd4a9bc5d"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "97af54574da94dbb0a8b5a5549e954b3"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons.dart", "hash": "26e8edddc50361d04ffdac680bcfeeca"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/button.dart", "hash": "212fbd7b9d63586beed8d0e16701bf05"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/collections.dart", "hash": "bb393d088799db4aa3e456bd768f1687"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0/lib/meta.dart", "hash": "8042ca366a626884c0e89628875af940"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/lib/services/auth_service.dart", "hash": "d8224e22d4848ed9220692655a9beb67"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "4c75638ad31731ec9908b311ea075a5c"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/icons.dart", "hash": "8aa656cf88546009f56e54df825c9915"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/annotations.dart", "hash": "b092b123c7d8046443429a9cd72baa9a"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "098ef2cc21af375e75e3fa80f2c8f12f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface.dart", "hash": "5145b27b3db429f9f1da26cfe563bd02"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "8e493e051c6c0cbe792781a7574a8f26"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/utils.dart", "hash": "8986177ba204a808c603c35260601cce"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "4c0d1712c28161aae922d6fb6aa513f3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ispellingerror.dart", "hash": "b78ba1985c8ec9afaa7beaa601fa8e00"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/build/flutter_assets/NOTICES.Z", "hash": "7d0f4e4100f8ac54fd918aca3c9a183a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/date_symbols.dart", "hash": "4c94c1ae460dd53255786f0ce3b53463"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/local.dart", "hash": "e81341d4c5ee8dc65f89ae4145cf2107"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/timeline.dart", "hash": "8d529903ab8e80df00c0859464bfbc46"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationelement7.dart", "hash": "cd0365e9895a1f44235bcf2288a11f66"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/predictive_back_page_transitions_builder.dart", "hash": "d113d4a197cc407c1012ca612db651ef"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/number_symbols.dart", "hash": "6c1b7903629a7ad4cb985f0898953db1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/shared_preferences_foundation.dart", "hash": "b72ebe27944e3a75601e56579bb92907"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/src/exception.dart", "hash": "5275d424aba5c931a30e6bd3e467027d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ishellitem.dart", "hash": "2ea28d523e25da87fbda7e73bc2ffedf"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/debug.dart", "hash": "d4b68da22867b9c51c88acc54eab3198"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/button_theme.dart", "hash": "02eac755aab5bcb73ac9a278a117ca1c"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "824c7dfb7d95a905ec1ba5f0d88aaf20"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system.dart", "hash": "06c73ad137e5db31d7e6ba4258ac13c7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/authentication_challenge.dart", "hash": "7bfefcc0929d945fa61bb7870de1f659"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/http.dart", "hash": "151d12284cf607a6e984aa31fe766faa"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "329b723b2cea0443e5ec2ccfb31fbfb8"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/no_splash.dart", "hash": "8217a1327affdcc17e4e9789ac490e7a"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/isolates.dart", "hash": "d76b7e85f343a184365107019b8117b8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/utils.dart", "hash": "ee746523b6e3d70f4061a5750c37cac3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imetadataassemblyimport.dart", "hash": "bcb3a959e03b0ba17fa42d5f919b2e00"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_datetime_picker_plus-2.2.0/lib/src/datetime_util.dart", "hash": "eff508fc1115905d52ab774ffad5085b"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/debug.dart", "hash": "3ff5c718c1926e8652d8ed393c236c7a"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_pixel_buffer_texture.h", "hash": "fb73b65a52e89c14449cd8524058187b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/imetadatadispenserex.dart", "hash": "6ee584441f30f72cea8a75f9b861591c"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "9fcf9265f470f44989cf4da88dd7cc0c"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/menu_style.dart", "hash": "04d4cbe0a7a40a31323cd39f2bb8d585"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ichannelaudiovolume.dart", "hash": "8ccaa7ec037755c10bf5586831be0fe1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_wsl_api_l1_1_0.g.dart", "hash": "6c31b298eba9c0df399049d9072d5ede"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtransformpattern.dart", "hash": "d374a7295ed13ae994b36d002890225f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/winspool.g.dart", "hash": "0f22a1dc771ec0ad975c574b1ce5dd70"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/layer.dart", "hash": "3c4c972da73c02c19c8597669d26a470"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/data_table_source.dart", "hash": "0145529858ad246065f7145bac7aef99"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/text_input.dart", "hash": "576b6e090503e6ebeba30801482d3b66"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/powrprof.g.dart", "hash": "2a4b4b7e76f1c79a762ce636f6b730db"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "e822107ed1c00c270f7e9ccfe670576c"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "2fd858cebb63269bf83de20ec9b36762"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/list_tile.dart", "hash": "bd2a37206b73cbcb99406f0b1ac68a09"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/algorithms.dart", "hash": "5fac07b9706002db32a4c5f6698cea58"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "94f8cadcbb0bd065c1a70e483d849960"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/path_provider_foundation.dart", "hash": "9485ecc20aafb0727c2700cf6e34cb65"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_foundation.dart", "hash": "db8ef5ac4d806e72f7b356056cb50b1f"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/pages.dart", "hash": "e50e0016353ca39f15e2e40e2221095e"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/slider.dart", "hash": "c5a71d0fdc87177c4ceabc19fbf2d251"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationtextrange2.dart", "hash": "afc3af0d3aaf8d64a82177a094097ee9"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/build/flutter_assets/shaders/ink_sparkle.frag", "hash": "6d10558fefdc90f0f18050bdc02f2447"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/number_format.dart", "hash": "76052188e777d0ca03128d3b299d836c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestproperties.dart", "hash": "3ec463d588e64344f9c833041d4c2e74"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/src/generated/top_level.dart", "hash": "3418e2ba1365bf8820838eae0da072fc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/macros.dart", "hash": "61161beafb5147bd9216c091fbe776c5"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "fc064178d31756adc0c680657e2a10d6"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "bc414bb2ae027237bbff00c7b2f2e2a5"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/debug.dart", "hash": "dbb0bb20c79bcea9397c34e3620c56c3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dislike/dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "bf4d44ff5dca3de072782665509d0a7b"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/restoration.dart", "hash": "89cb61ecfad952e35daf52caac600894"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/input_decorator.dart", "hash": "289dd9662b900b118f3042be16a73987"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomation2.dart", "hash": "34d140191c4affc37f3716de1b46854a"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "2f510aaa9b6e989e33c72b16c8b5b040"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/pinned_header_sliver.dart", "hash": "77aae98676ea4f6ece383935c23c1978"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "2f2ab157e82e8fdf20b247a00e5bde3c"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/flavor.dart", "hash": "4744aaec510cd9c8b07ca129362b8fb9"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/debug.dart", "hash": "0f366f928cc93bceb62726d94a7bc889"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_engine.h", "hash": "1cb444a4e10159155428882dc38a06bc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/lib/src/clock.dart", "hash": "1355e7c034fb490a3da4b604bf4a245e"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/divider_theme.dart", "hash": "b794bf7c553a2a0acab8dbfef6b0af0e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestreader4.dart", "hash": "c475dfaacb936bfc5773b55b5b7db7a3"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "34517b36f5fc8d574ff2ffaadcd2b9a0"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "ce2f733c9ef461c84bb85a93bae9e7a9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/variant.dart", "hash": "68048a53f754265a484cc5c4798db6af"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "2a90f95a9de0d2364fee5e1ddbab0c18"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/constants.dart", "hash": "d10f9a1cfa41452535b1fa21db8e1c8d"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/app.dart", "hash": "40a19c80c10d11c793a04d0d81a8c16e"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "36059bfe2991ae3c2a5d18941ef03857"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationnotcondition.dart", "hash": "6aa37695d4ecfd1cd9514e2758da9f5c"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/system_context_menu.dart", "hash": "2854423575c903818dc71d712b749e71"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationobjectmodelpattern.dart", "hash": "db8a81e510b416095ef477688165eee5"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "8166d4859a89eef9e25697932c522bce"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_core_winrt_error_l1_1_0.g.dart", "hash": "0c553b8a000e02d64689984657b137a6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/tabs.dart", "hash": "ccf1ac4d4f2404b45b65e33a068b3e8d"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/object.dart", "hash": "a22f8a00622b28aaa715b7587b658468"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/flutter/packages/flutter/lib/material.dart", "hash": "f3e5196336dc300b73d52ee30ae05c33"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "d8f8a80ad0c05f281d58e8f9e20b8b14"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_core_sysinfo_l1_2_3.g.dart", "hash": "f22a66d83ebf3e0455559a16f49e70bd"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/unicode.dart", "hash": "8b525140e1bf7268e1681a62c7640eea"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "6b92d8f12a7fb46649297e25d2cf2b34"}, {"path": "/opt/flutter/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_binary_messenger.h", "hash": "d18aca92fddd9924e6bdefb1bb7c9a00"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "a8abf3052eb97ac137ee94d906fcbd08"}, {"path": "/opt/flutter/bin/cache/pkg/sky_engine/LICENSE", "hash": "b7add65f976680b338d238c4db63c68c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/string_stack.dart", "hash": "aa27dfc54687394062db977707839be5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/unmodifiable_wrappers.dart", "hash": "4be4077b482b12a5ee202d859e8286df"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/undo_history.dart", "hash": "932fb518b9857889d8182a4d9d78d2d9"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "97fb5affb10598f0356958c2e7e542b2"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/material_localizations.dart", "hash": "17f28ff1f00166c9ef3955185daf21e0"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "81ca0774ea5d4ff5f061ac7b53957e6f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationrangevaluepattern.dart", "hash": "f90b22ce5704e97441e7e2265d0119e7"}, {"path": "/opt/flutter/packages/flutter/lib/src/painting/_network_image_io.dart", "hash": "682907a0e9e60ab53b752dde1e45381a"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_event_channel.h", "hash": "defbac680fc3c78d98ef61319ff0dbb6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iwbemhiperfenum.dart", "hash": "2b344fedd3805594c1c2981f8c06f148"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iunknown.dart", "hash": "4c90e2a275589188bd8714dd9cc5650a"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "hash": "c662670b9313c57d5d61b85f539a5797"}, {"path": "/opt/flutter/packages/flutter/lib/painting.dart", "hash": "443c2884ba8c15b3369e06601ffd88f7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_async_foundation.dart", "hash": "282aeeb78f4a92064354b5fe98161484"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/shared_preferences_windows.dart", "hash": "2bc47cc0ce47761990162c3f08072016"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iappxmanifestpackageid.dart", "hash": "74afb02c8643f153de3fb64ad8a466a6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ifiledialog.dart", "hash": "dd9bdb173b854917c11832f369e59479"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "53b46c9af9e99869b44e05cfefbd7d46"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/lib/src/default.dart", "hash": "a485f5c7307db6bbba1d3388b528a770"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationscrollpattern.dart", "hash": "106d1bdb4f9d839cf4a65252635f965c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iuiautomationcacherequest.dart", "hash": "bec9a4fa9a224f42d622cf676a494a2a"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "3455df89df3647c70f9d2586275523b9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.5.0/lib/src/streamed_request.dart", "hash": "a93ae192d60f10b56cf1659d2123bc95"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/src/highlighter.dart", "hash": "5265b4bdec5c90bfd2937f140f3ba8fc"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/src/intl/number_format_parser.dart", "hash": "61a0deef2a4f0ebaed506bb2a22c5185"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudioclock.dart", "hash": "7c32424ef2aaa2f268fe177af2d4731f"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/text_editing.dart", "hash": "a1497040765f0f7199f990aa00af5310"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/platform_channel.dart", "hash": "c7060506da9f5033615367bcfec355d9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/iaudiorenderclient.dart", "hash": "678125b16711755ee7950f73890a3360"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/media_type.dart", "hash": "101ff6d49da9d3040faf0722153efee7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/canonicalized_map.dart", "hash": "889042dc1cc5b1f4e4e1572270920f54"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/guid.dart", "hash": "e5a79b510256712e5dbab68965722534"}, {"path": "/opt/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "4a3809e55c89557f70a08060ea2102a8"}, {"path": "/opt/flutter/packages/flutter/lib/src/semantics/semantics.dart", "hash": "0381c11b6d4f8c985d498d475bc82822"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "a7a9eeb4bfc63b4f552162a16b62f70a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/ienumspellingerror.dart", "hash": "c2b3370ba518e83a18e0be246f0e2ed4"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "11a634821b3bce05dac94f3dabe52a75"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h", "hash": "44b9ac9a008150255bb2e40ad145b47c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/com/idesktopwallpaper.dart", "hash": "74319ce8573194302792ea41f665838b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/exceptions.dart", "hash": "7c49607737f1eac9820d787b1f2854eb"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/lib/screens/dashboard_screen.dart", "hash": "25e404a890bed24d647710094cd239f7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "/opt/flutter/packages/flutter/lib/src/foundation/assertions.dart", "hash": "f5dfb544b24fa84a9f504c5b8be1d838"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_0.g.dart", "hash": "872e5481eca8f8f757d139b5e4988053"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/src/http_date.dart", "hash": "fb76e9ed5173ac1ae6a6f43288581808"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/enums.dart", "hash": "a71d2292a5f598a6eea9a8ce5f3c5783"}, {"path": "/home/<USER>/Desktop/VidyaArena/vidya_arena/lib/models/api_response.dart", "hash": "fad4683bdab6abdd37a8246ed1794a75"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.1/lib/src/combase.dart", "hash": "10ffd776cef80532a293c5673825e655"}, {"path": "/opt/flutter/packages/flutter/lib/src/material/radio.dart", "hash": "228535e61392d353222c93613f49eb9b"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/navigator.dart", "hash": "7ae2142321839f48597dced1087444b1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "hash": "b5a5a0c9d78c8329f5d91094bfd4b820"}, {"path": "/opt/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "366aa23421c294b9ad3fa22271afbdb3"}, {"path": "/opt/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "55f6efa34626128dab35b6985aaf09db"}, {"path": "/opt/flutter/packages/flutter/lib/src/rendering/editable.dart", "hash": "c0d8bc79666e26dcbc4f56428478d7b7"}, {"path": "/opt/flutter/packages/flutter/lib/src/gestures/long_press.dart", "hash": "be262921a4471e495202bfe1871b610e"}]}