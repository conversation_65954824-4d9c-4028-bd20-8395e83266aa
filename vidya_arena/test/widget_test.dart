// This is a basic Flutter widget test for VidyaArena app.

import 'package:flutter_test/flutter_test.dart';

import 'package:vidya_arena/main.dart';

void main() {
  testWidgets('VidyaArena app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const VidyaArenaApp());

    // Verify that the welcome text is displayed.
    expect(find.text('Welcome to VidyaArena'), findsOneWidget);
    expect(find.text("India's only real-time educational gaming platform."), findsOneWidget);

    // Verify that login and register buttons are present.
    expect(find.text('Login'), findsOneWidget);
    expect(find.text('Register'), findsOneWidget);
  });
}
