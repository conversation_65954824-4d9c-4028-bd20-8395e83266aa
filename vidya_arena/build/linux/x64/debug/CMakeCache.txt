# This is the CMakeCache file.
# For build in directory: /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Debug

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/clang++

//LLVM archiver
CMAKE_CXX_COMPILER_AR:FILEPATH=CMAKE_CXX_COMPILER_AR-NOTFOUND

//`clang-scan-deps` dependency scanner
CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS:FILEPATH=/usr/bin/clang-scan-deps

//Generate index for LLVM archive
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=CMAKE_CXX_COMPILER_RANLIB-NOTFOUND

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of build database during the build.
CMAKE_EXPORT_BUILD_DATABASE:BOOL=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/CMakeFiles/pkgRedirects

//...
CMAKE_INSTALL_PREFIX:PATH=/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/bundle

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Program used to build from build.ninja files.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/ninja

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_COMPAT_VERSION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=runner

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the archiver during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the archiver during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the archiver during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the archiver during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the archiver during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=CMAKE_TAPI-NOTFOUND

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//No help, variable specified on the command line.
FLUTTER_TARGET_PLATFORM:UNINITIALIZED=linux-x64

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//Value Computed by CMake
flutter_secure_storage_linux_BINARY_DIR:STATIC=/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/plugins/flutter_secure_storage_linux

//Value Computed by CMake
flutter_secure_storage_linux_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
flutter_secure_storage_linux_SOURCE_DIR:STATIC=/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux

//Path to a library.
pkgcfg_lib_GIO_gio-2.0:FILEPATH=/usr/lib/libgio-2.0.so

//Path to a library.
pkgcfg_lib_GIO_glib-2.0:FILEPATH=/usr/lib/libglib-2.0.so

//Path to a library.
pkgcfg_lib_GIO_gobject-2.0:FILEPATH=/usr/lib/libgobject-2.0.so

//Path to a library.
pkgcfg_lib_GLIB_glib-2.0:FILEPATH=/usr/lib/libglib-2.0.so

//Path to a library.
pkgcfg_lib_GTK_atk-1.0:FILEPATH=/usr/lib/libatk-1.0.so

//Path to a library.
pkgcfg_lib_GTK_cairo:FILEPATH=/usr/lib/libcairo.so

//Path to a library.
pkgcfg_lib_GTK_cairo-gobject:FILEPATH=/usr/lib/libcairo-gobject.so

//Path to a library.
pkgcfg_lib_GTK_gdk-3:FILEPATH=/usr/lib/libgdk-3.so

//Path to a library.
pkgcfg_lib_GTK_gdk_pixbuf-2.0:FILEPATH=/usr/lib/libgdk_pixbuf-2.0.so

//Path to a library.
pkgcfg_lib_GTK_gio-2.0:FILEPATH=/usr/lib/libgio-2.0.so

//Path to a library.
pkgcfg_lib_GTK_glib-2.0:FILEPATH=/usr/lib/libglib-2.0.so

//Path to a library.
pkgcfg_lib_GTK_gobject-2.0:FILEPATH=/usr/lib/libgobject-2.0.so

//Path to a library.
pkgcfg_lib_GTK_gtk-3:FILEPATH=/usr/lib/libgtk-3.so

//Path to a library.
pkgcfg_lib_GTK_harfbuzz:FILEPATH=/usr/lib/libharfbuzz.so

//Path to a library.
pkgcfg_lib_GTK_pango-1.0:FILEPATH=/usr/lib/libpango-1.0.so

//Path to a library.
pkgcfg_lib_GTK_pangocairo-1.0:FILEPATH=/usr/lib/libpangocairo-1.0.so

//Path to a library.
pkgcfg_lib_GTK_z:FILEPATH=/usr/lib/libz.so

//Path to a library.
pkgcfg_lib_LIBSECRET_gio-2.0:FILEPATH=/usr/lib/libgio-2.0.so

//Path to a library.
pkgcfg_lib_LIBSECRET_glib-2.0:FILEPATH=/usr/lib/libglib-2.0.so

//Path to a library.
pkgcfg_lib_LIBSECRET_gobject-2.0:FILEPATH=/usr/lib/libgobject-2.0.so

//Path to a library.
pkgcfg_lib_LIBSECRET_secret-1:FILEPATH=/usr/lib/libsecret-1.so

//Value Computed by CMake
runner_BINARY_DIR:STATIC=/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug

//Value Computed by CMake
runner_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
runner_SOURCE_DIR:STATIC=/home/<USER>/Desktop/VidyaArena/vidya_arena/linux


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=1
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS
CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/usr/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_BUILD_DATABASE
CMAKE_EXPORT_BUILD_DATABASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/Desktop/VidyaArena/vidya_arena/linux
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=0
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=3
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[/usr/bin/pkg-config][v2.5.1()]
GIO_CFLAGS:INTERNAL=-I/usr/include;-I/usr/include/glib-2.0;-I/usr/lib/glib-2.0/include;-I/usr/include/libmount;-I/usr/include/blkid;-I/usr/include/sysprof-6;-pthread
GIO_CFLAGS_I:INTERNAL=
GIO_CFLAGS_OTHER:INTERNAL=-pthread
GIO_FOUND:INTERNAL=1
GIO_INCLUDEDIR:INTERNAL=/usr/include
GIO_INCLUDE_DIRS:INTERNAL=/usr/include;/usr/include/glib-2.0;/usr/lib/glib-2.0/include;/usr/include/libmount;/usr/include/blkid;/usr/include/sysprof-6
GIO_LDFLAGS:INTERNAL=-L/usr/lib;-lgio-2.0;-lgobject-2.0;-lglib-2.0
GIO_LDFLAGS_OTHER:INTERNAL=
GIO_LIBDIR:INTERNAL=/usr/lib
GIO_LIBRARIES:INTERNAL=gio-2.0;gobject-2.0;glib-2.0
GIO_LIBRARY_DIRS:INTERNAL=/usr/lib
GIO_LIBS:INTERNAL=
GIO_LIBS_L:INTERNAL=
GIO_LIBS_OTHER:INTERNAL=
GIO_LIBS_PATHS:INTERNAL=
GIO_MODULE_NAME:INTERNAL=gio-2.0
GIO_PREFIX:INTERNAL=/usr
GIO_STATIC_CFLAGS:INTERNAL=-I/usr/include;-I/usr/include/glib-2.0;-I/usr/lib/glib-2.0/include;-I/usr/include/libmount;-I/usr/include/blkid;-I/usr/include/sysprof-6;-pthread
GIO_STATIC_CFLAGS_I:INTERNAL=
GIO_STATIC_CFLAGS_OTHER:INTERNAL=-pthread
GIO_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include;/usr/include/glib-2.0;/usr/lib/glib-2.0/include;/usr/include/libmount;/usr/include/blkid;/usr/include/sysprof-6
GIO_STATIC_LDFLAGS:INTERNAL=-L/usr/lib;-lgio-2.0;-lgobject-2.0;-lffi;-lgmodule-2.0;-pthread;-lglib-2.0;-lm;-lpcre2-8;-lz;-lmount;-lblkid;-lsysprof-capture-4;-pthread
GIO_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread;-pthread
GIO_STATIC_LIBDIR:INTERNAL=
GIO_STATIC_LIBRARIES:INTERNAL=gio-2.0;gobject-2.0;ffi;gmodule-2.0;glib-2.0;m;pcre2-8;z;mount;blkid;sysprof-capture-4
GIO_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib
GIO_STATIC_LIBS:INTERNAL=
GIO_STATIC_LIBS_L:INTERNAL=
GIO_STATIC_LIBS_OTHER:INTERNAL=
GIO_STATIC_LIBS_PATHS:INTERNAL=
GIO_VERSION:INTERNAL=2.84.1
GIO_gio-2.0_INCLUDEDIR:INTERNAL=
GIO_gio-2.0_LIBDIR:INTERNAL=
GIO_gio-2.0_PREFIX:INTERNAL=
GIO_gio-2.0_VERSION:INTERNAL=
GLIB_CFLAGS:INTERNAL=-I/usr/include/glib-2.0;-I/usr/lib/glib-2.0/include;-I/usr/include/sysprof-6;-pthread;-I/usr/include
GLIB_CFLAGS_I:INTERNAL=
GLIB_CFLAGS_OTHER:INTERNAL=-pthread
GLIB_FOUND:INTERNAL=1
GLIB_INCLUDEDIR:INTERNAL=/usr/include
GLIB_INCLUDE_DIRS:INTERNAL=/usr/include/glib-2.0;/usr/lib/glib-2.0/include;/usr/include/sysprof-6;/usr/include
GLIB_LDFLAGS:INTERNAL=-L/usr/lib;-lglib-2.0
GLIB_LDFLAGS_OTHER:INTERNAL=
GLIB_LIBDIR:INTERNAL=/usr/lib
GLIB_LIBRARIES:INTERNAL=glib-2.0
GLIB_LIBRARY_DIRS:INTERNAL=/usr/lib
GLIB_LIBS:INTERNAL=
GLIB_LIBS_L:INTERNAL=
GLIB_LIBS_OTHER:INTERNAL=
GLIB_LIBS_PATHS:INTERNAL=
GLIB_MODULE_NAME:INTERNAL=glib-2.0
GLIB_PREFIX:INTERNAL=/usr
GLIB_STATIC_CFLAGS:INTERNAL=-I/usr/include/glib-2.0;-I/usr/lib/glib-2.0/include;-I/usr/include/sysprof-6;-pthread;-I/usr/include
GLIB_STATIC_CFLAGS_I:INTERNAL=
GLIB_STATIC_CFLAGS_OTHER:INTERNAL=-pthread
GLIB_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/glib-2.0;/usr/lib/glib-2.0/include;/usr/include/sysprof-6;/usr/include
GLIB_STATIC_LDFLAGS:INTERNAL=-L/usr/lib;-lglib-2.0;-lm;-lsysprof-capture-4;-pthread;-lpcre2-8
GLIB_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
GLIB_STATIC_LIBDIR:INTERNAL=
GLIB_STATIC_LIBRARIES:INTERNAL=glib-2.0;m;sysprof-capture-4;pcre2-8
GLIB_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib
GLIB_STATIC_LIBS:INTERNAL=
GLIB_STATIC_LIBS_L:INTERNAL=
GLIB_STATIC_LIBS_OTHER:INTERNAL=
GLIB_STATIC_LIBS_PATHS:INTERNAL=
GLIB_VERSION:INTERNAL=2.84.1
GLIB_glib-2.0_INCLUDEDIR:INTERNAL=
GLIB_glib-2.0_LIBDIR:INTERNAL=
GLIB_glib-2.0_PREFIX:INTERNAL=
GLIB_glib-2.0_VERSION:INTERNAL=
GTK_CFLAGS:INTERNAL=-I/usr/include/gtk-3.0;-I/usr/include/pango-1.0;-I/usr/include;-I/usr/include/cloudproviders;-I/usr/include/cairo;-I/usr/include/gdk-pixbuf-2.0;-I/usr/include/at-spi2-atk/2.0;-I/usr/include/at-spi-2.0;-I/usr/include/atk-1.0;-I/usr/include/dbus-1.0;-I/usr/lib/dbus-1.0/include;-I/usr/include/fribidi;-I/usr/include/pixman-1;-I/usr/include/harfbuzz;-I/usr/include/freetype2;-I/usr/include/libpng16;-I/usr/include/gio-unix-2.0;-I/usr/include/glib-2.0;-I/usr/lib/glib-2.0/include;-I/usr/include/libmount;-I/usr/include/blkid;-I/usr/include/sysprof-6;-pthread
GTK_CFLAGS_I:INTERNAL=
GTK_CFLAGS_OTHER:INTERNAL=-pthread
GTK_FOUND:INTERNAL=1
GTK_INCLUDEDIR:INTERNAL=/usr/include
GTK_INCLUDE_DIRS:INTERNAL=/usr/include/gtk-3.0;/usr/include/pango-1.0;/usr/include;/usr/include/cloudproviders;/usr/include/cairo;/usr/include/gdk-pixbuf-2.0;/usr/include/at-spi2-atk/2.0;/usr/include/at-spi-2.0;/usr/include/atk-1.0;/usr/include/dbus-1.0;/usr/lib/dbus-1.0/include;/usr/include/fribidi;/usr/include/pixman-1;/usr/include/harfbuzz;/usr/include/freetype2;/usr/include/libpng16;/usr/include/gio-unix-2.0;/usr/include/glib-2.0;/usr/lib/glib-2.0/include;/usr/include/libmount;/usr/include/blkid;/usr/include/sysprof-6
GTK_LDFLAGS:INTERNAL=-L/usr/lib;-lgtk-3;-lgdk-3;-lz;-lpangocairo-1.0;-lcairo-gobject;-lgdk_pixbuf-2.0;-latk-1.0;-lpango-1.0;-lcairo;-lharfbuzz;-lgio-2.0;-lgobject-2.0;-lglib-2.0
GTK_LDFLAGS_OTHER:INTERNAL=
GTK_LIBDIR:INTERNAL=/usr/lib
GTK_LIBRARIES:INTERNAL=gtk-3;gdk-3;z;pangocairo-1.0;cairo-gobject;gdk_pixbuf-2.0;atk-1.0;pango-1.0;cairo;harfbuzz;gio-2.0;gobject-2.0;glib-2.0
GTK_LIBRARY_DIRS:INTERNAL=/usr/lib
GTK_LIBS:INTERNAL=
GTK_LIBS_L:INTERNAL=
GTK_LIBS_OTHER:INTERNAL=
GTK_LIBS_PATHS:INTERNAL=
GTK_MODULE_NAME:INTERNAL=gtk+-3.0
GTK_PREFIX:INTERNAL=/usr
GTK_STATIC_CFLAGS:INTERNAL=-I/usr/include/gtk-3.0;-I/usr/include/pango-1.0;-I/usr/include;-I/usr/include/cloudproviders;-I/usr/include/cairo;-I/usr/include/gdk-pixbuf-2.0;-I/usr/include/at-spi2-atk/2.0;-I/usr/include/at-spi-2.0;-I/usr/include/atk-1.0;-I/usr/include/dbus-1.0;-I/usr/lib/dbus-1.0/include;-I/usr/include/fribidi;-I/usr/include/pixman-1;-I/usr/include/harfbuzz;-I/usr/include/freetype2;-I/usr/include/libpng16;-I/usr/include/gio-unix-2.0;-I/usr/include/glib-2.0;-I/usr/lib/glib-2.0/include;-I/usr/include/libmount;-I/usr/include/blkid;-I/usr/include/sysprof-6;-pthread;-DLZMA_API_STATIC;-DXML_STATIC
GTK_STATIC_CFLAGS_I:INTERNAL=
GTK_STATIC_CFLAGS_OTHER:INTERNAL=-pthread;-DLZMA_API_STATIC;-DXML_STATIC
GTK_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/gtk-3.0;/usr/include/pango-1.0;/usr/include;/usr/include/cloudproviders;/usr/include/cairo;/usr/include/gdk-pixbuf-2.0;/usr/include/at-spi2-atk/2.0;/usr/include/at-spi-2.0;/usr/include/atk-1.0;/usr/include/dbus-1.0;/usr/lib/dbus-1.0/include;/usr/include/fribidi;/usr/include/pixman-1;/usr/include/harfbuzz;/usr/include/freetype2;/usr/include/libpng16;/usr/include/gio-unix-2.0;/usr/include/glib-2.0;/usr/lib/glib-2.0/include;/usr/include/libmount;/usr/include/blkid;/usr/include/sysprof-6
GTK_STATIC_LDFLAGS:INTERNAL=-L/usr/lib;-lgtk-3;-lgdk-3;-lz;-lpangocairo-1.0;-lm;-lXrandr;-lXcursor;-lXcomposite;-lXdamage;-lXinerama;-lcloudproviders;-lcairo-gobject;-lm;-ldl;-lgdk_pixbuf-2.0;-lm;-ltiff;-ljbig;-lz;-lm;-lzstd;-llzma;-pthread;-lpthread;-ljpeg;-latk-bridge-2.0;-latspi;-lXtst;-lXi;-lXfixes;-latk-1.0;-ldbus-1;-pthread;-lsystemd;-Wl,--export-dynamic;-lxkbcommon;-lwayland-cursor;-lwayland-egl;-lwayland-client;-lm;-pthread;-lrt;-lepoxy;-ldl;-lGL;-lEGL;-lpangoft2-1.0;-lm;-lpango-1.0;-lm;-lfribidi;-lthai;-ldatrie;-lXft;-lcairo;-lm;-ldl;-lfontconfig;-pthread;-lm;-lexpat;-lm;-lXext;-lXrender;-lX11;-lpthread;-lxcb-render;-lxcb-shm;-lxcb;-lXau;-lXdmcp;-lpixman-1;-lm;-pthread;-lharfbuzz-gobject;-lharfbuzz;-lm;-lfreetype;-lbz2;-lpng16;-lm;-lm;-lbrotlidec;-lbrotlicommon;-lgraphite2;-lgio-2.0;-lgobject-2.0;-lffi;-lgmodule-2.0;-pthread;-lglib-2.0;-lm;-lpcre2-8;-lz;-lmount;-lblkid;-lsysprof-capture-4;-pthread
GTK_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread;-pthread;-Wl,--export-dynamic;-pthread;-pthread;-pthread;-pthread;-pthread
GTK_STATIC_LIBDIR:INTERNAL=
GTK_STATIC_LIBRARIES:INTERNAL=gtk-3;gdk-3;z;pangocairo-1.0;m;Xrandr;Xcursor;Xcomposite;Xdamage;Xinerama;cloudproviders;cairo-gobject;m;dl;gdk_pixbuf-2.0;m;tiff;jbig;z;m;zstd;lzma;pthread;jpeg;atk-bridge-2.0;atspi;Xtst;Xi;Xfixes;atk-1.0;dbus-1;systemd;xkbcommon;wayland-cursor;wayland-egl;wayland-client;m;rt;epoxy;dl;GL;EGL;pangoft2-1.0;m;pango-1.0;m;fribidi;thai;datrie;Xft;cairo;m;dl;fontconfig;m;expat;m;Xext;Xrender;X11;pthread;xcb-render;xcb-shm;xcb;Xau;Xdmcp;pixman-1;m;harfbuzz-gobject;harfbuzz;m;freetype;bz2;png16;m;m;brotlidec;brotlicommon;graphite2;gio-2.0;gobject-2.0;ffi;gmodule-2.0;glib-2.0;m;pcre2-8;z;mount;blkid;sysprof-capture-4
GTK_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib
GTK_STATIC_LIBS:INTERNAL=
GTK_STATIC_LIBS_L:INTERNAL=
GTK_STATIC_LIBS_OTHER:INTERNAL=
GTK_STATIC_LIBS_PATHS:INTERNAL=
GTK_VERSION:INTERNAL=3.24.50
GTK_gtk+-3.0_INCLUDEDIR:INTERNAL=
GTK_gtk+-3.0_LIBDIR:INTERNAL=
GTK_gtk+-3.0_PREFIX:INTERNAL=
GTK_gtk+-3.0_VERSION:INTERNAL=
LIBSECRET_CFLAGS:INTERNAL=-I/usr/include/libsecret-1;-I/usr/include/gio-unix-2.0;-I/usr/include;-I/usr/include/glib-2.0;-I/usr/lib/glib-2.0/include;-I/usr/include/libmount;-I/usr/include/blkid;-I/usr/include/sysprof-6;-pthread;-I/usr/include/tss2
LIBSECRET_CFLAGS_I:INTERNAL=
LIBSECRET_CFLAGS_OTHER:INTERNAL=-pthread
LIBSECRET_FOUND:INTERNAL=1
LIBSECRET_INCLUDEDIR:INTERNAL=/usr/include
LIBSECRET_INCLUDE_DIRS:INTERNAL=/usr/include/libsecret-1;/usr/include/gio-unix-2.0;/usr/include;/usr/include/glib-2.0;/usr/lib/glib-2.0/include;/usr/include/libmount;/usr/include/blkid;/usr/include/sysprof-6;/usr/include/tss2
LIBSECRET_LDFLAGS:INTERNAL=-L/usr/lib;-lsecret-1;-lgio-2.0;-lgobject-2.0;-lglib-2.0
LIBSECRET_LDFLAGS_OTHER:INTERNAL=
LIBSECRET_LIBDIR:INTERNAL=/usr/lib
LIBSECRET_LIBRARIES:INTERNAL=secret-1;gio-2.0;gobject-2.0;glib-2.0
LIBSECRET_LIBRARY_DIRS:INTERNAL=/usr/lib
LIBSECRET_LIBS:INTERNAL=
LIBSECRET_LIBS_L:INTERNAL=
LIBSECRET_LIBS_OTHER:INTERNAL=
LIBSECRET_LIBS_PATHS:INTERNAL=
LIBSECRET_MODULE_NAME:INTERNAL=libsecret-1
LIBSECRET_PREFIX:INTERNAL=/usr
LIBSECRET_STATIC_CFLAGS:INTERNAL=-I/usr/include/libsecret-1;-I/usr/include/gio-unix-2.0;-I/usr/include;-I/usr/include/glib-2.0;-I/usr/lib/glib-2.0/include;-I/usr/include/libmount;-I/usr/include/blkid;-I/usr/include/sysprof-6;-pthread;-I/usr/include/tss2
LIBSECRET_STATIC_CFLAGS_I:INTERNAL=
LIBSECRET_STATIC_CFLAGS_OTHER:INTERNAL=-pthread
LIBSECRET_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/libsecret-1;/usr/include/gio-unix-2.0;/usr/include;/usr/include/glib-2.0;/usr/lib/glib-2.0/include;/usr/include/libmount;/usr/include/blkid;/usr/include/sysprof-6;/usr/include/tss2
LIBSECRET_STATIC_LDFLAGS:INTERNAL=-L/usr/lib;-lsecret-1;-lgio-2.0;-lgobject-2.0;-lffi;-lgmodule-2.0;-pthread;-lglib-2.0;-lm;-lpcre2-8;-lz;-lmount;-lblkid;-lsysprof-capture-4;-pthread;-lgcrypt;-lgpg-error;-ltss2-esys;-lcrypto;-ltss2-sys;-ltss2-mu;-ltss2-rc;-ltss2-tctildr
LIBSECRET_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread;-pthread
LIBSECRET_STATIC_LIBDIR:INTERNAL=
LIBSECRET_STATIC_LIBRARIES:INTERNAL=secret-1;gio-2.0;gobject-2.0;ffi;gmodule-2.0;glib-2.0;m;pcre2-8;z;mount;blkid;sysprof-capture-4;gcrypt;gpg-error;tss2-esys;crypto;tss2-sys;tss2-mu;tss2-rc;tss2-tctildr
LIBSECRET_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib
LIBSECRET_STATIC_LIBS:INTERNAL=
LIBSECRET_STATIC_LIBS_L:INTERNAL=
LIBSECRET_STATIC_LIBS_OTHER:INTERNAL=
LIBSECRET_STATIC_LIBS_PATHS:INTERNAL=
LIBSECRET_VERSION:INTERNAL=0.21.7
LIBSECRET_libsecret-1_INCLUDEDIR:INTERNAL=
LIBSECRET_libsecret-1_LIBDIR:INTERNAL=
LIBSECRET_libsecret-1_PREFIX:INTERNAL=
LIBSECRET_libsecret-1_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
__pkg_config_arguments_GIO:INTERNAL=REQUIRED;IMPORTED_TARGET;gio-2.0
__pkg_config_arguments_GLIB:INTERNAL=REQUIRED;IMPORTED_TARGET;glib-2.0
__pkg_config_arguments_GTK:INTERNAL=REQUIRED;IMPORTED_TARGET;gtk+-3.0
__pkg_config_arguments_LIBSECRET:INTERNAL=REQUIRED;IMPORTED_TARGET;libsecret-1>=0.18.4
__pkg_config_checked_GIO:INTERNAL=1
__pkg_config_checked_GLIB:INTERNAL=1
__pkg_config_checked_GTK:INTERNAL=1
__pkg_config_checked_LIBSECRET:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GIO_gio-2.0
pkgcfg_lib_GIO_gio-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GIO_glib-2.0
pkgcfg_lib_GIO_glib-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GIO_gobject-2.0
pkgcfg_lib_GIO_gobject-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GLIB_glib-2.0
pkgcfg_lib_GLIB_glib-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_atk-1.0
pkgcfg_lib_GTK_atk-1.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_cairo
pkgcfg_lib_GTK_cairo-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_cairo-gobject
pkgcfg_lib_GTK_cairo-gobject-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_gdk-3
pkgcfg_lib_GTK_gdk-3-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_gdk_pixbuf-2.0
pkgcfg_lib_GTK_gdk_pixbuf-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_gio-2.0
pkgcfg_lib_GTK_gio-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_glib-2.0
pkgcfg_lib_GTK_glib-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_gobject-2.0
pkgcfg_lib_GTK_gobject-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_gtk-3
pkgcfg_lib_GTK_gtk-3-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_harfbuzz
pkgcfg_lib_GTK_harfbuzz-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_pango-1.0
pkgcfg_lib_GTK_pango-1.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_pangocairo-1.0
pkgcfg_lib_GTK_pangocairo-1.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK_z
pkgcfg_lib_GTK_z-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_LIBSECRET_gio-2.0
pkgcfg_lib_LIBSECRET_gio-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_LIBSECRET_glib-2.0
pkgcfg_lib_LIBSECRET_glib-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_LIBSECRET_gobject-2.0
pkgcfg_lib_LIBSECRET_gobject-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_LIBSECRET_secret-1
pkgcfg_lib_LIBSECRET_secret-1-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib

