# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.1

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: runner
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/
# =============================================================================
# Object build statements for EXECUTABLE target vidya_arena


#############################################
# Order-only phony target for vidya_arena

build cmake_object_order_depends_target_vidya_arena: phony || cmake_object_order_depends_target_flutter_secure_storage_linux_plugin flutter/flutter_assemble

build CMakeFiles/vidya_arena.dir/main.cc.o: CXX_COMPILER__vidya_arena_unscanned_Debug /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/main.cc || cmake_object_order_depends_target_vidya_arena
  CONFIG = Debug
  DEFINES = -DAPPLICATION_ID=\"com.example.vidya_arena\"
  DEP_FILE = CMakeFiles/vidya_arena.dir/main.cc.o.d
  FLAGS = -g -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral -I/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux/include -isystem /usr/include/gtk-3.0 -isystem /usr/include/pango-1.0 -isystem /usr/include/cloudproviders -isystem /usr/include/cairo -isystem /usr/include/gdk-pixbuf-2.0 -isystem /usr/include/at-spi2-atk/2.0 -isystem /usr/include/at-spi-2.0 -isystem /usr/include/atk-1.0 -isystem /usr/include/dbus-1.0 -isystem /usr/lib/dbus-1.0/include -isystem /usr/include/fribidi -isystem /usr/include/pixman-1 -isystem /usr/include/harfbuzz -isystem /usr/include/freetype2 -isystem /usr/include/libpng16 -isystem /usr/include/gio-unix-2.0 -isystem /usr/include/glib-2.0 -isystem /usr/lib/glib-2.0/include -isystem /usr/include/libmount -isystem /usr/include/blkid -isystem /usr/include/sysprof-6
  OBJECT_DIR = CMakeFiles/vidya_arena.dir
  OBJECT_FILE_DIR = CMakeFiles/vidya_arena.dir
  TARGET_COMPILE_PDB = CMakeFiles/vidya_arena.dir/
  TARGET_PDB = intermediates_do_not_run/vidya_arena.pdb

build CMakeFiles/vidya_arena.dir/my_application.cc.o: CXX_COMPILER__vidya_arena_unscanned_Debug /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/my_application.cc || cmake_object_order_depends_target_vidya_arena
  CONFIG = Debug
  DEFINES = -DAPPLICATION_ID=\"com.example.vidya_arena\"
  DEP_FILE = CMakeFiles/vidya_arena.dir/my_application.cc.o.d
  FLAGS = -g -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral -I/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux/include -isystem /usr/include/gtk-3.0 -isystem /usr/include/pango-1.0 -isystem /usr/include/cloudproviders -isystem /usr/include/cairo -isystem /usr/include/gdk-pixbuf-2.0 -isystem /usr/include/at-spi2-atk/2.0 -isystem /usr/include/at-spi-2.0 -isystem /usr/include/atk-1.0 -isystem /usr/include/dbus-1.0 -isystem /usr/lib/dbus-1.0/include -isystem /usr/include/fribidi -isystem /usr/include/pixman-1 -isystem /usr/include/harfbuzz -isystem /usr/include/freetype2 -isystem /usr/include/libpng16 -isystem /usr/include/gio-unix-2.0 -isystem /usr/include/glib-2.0 -isystem /usr/lib/glib-2.0/include -isystem /usr/include/libmount -isystem /usr/include/blkid -isystem /usr/include/sysprof-6
  OBJECT_DIR = CMakeFiles/vidya_arena.dir
  OBJECT_FILE_DIR = CMakeFiles/vidya_arena.dir
  TARGET_COMPILE_PDB = CMakeFiles/vidya_arena.dir/
  TARGET_PDB = intermediates_do_not_run/vidya_arena.pdb

build CMakeFiles/vidya_arena.dir/flutter/generated_plugin_registrant.cc.o: CXX_COMPILER__vidya_arena_unscanned_Debug /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/generated_plugin_registrant.cc || cmake_object_order_depends_target_vidya_arena
  CONFIG = Debug
  DEFINES = -DAPPLICATION_ID=\"com.example.vidya_arena\"
  DEP_FILE = CMakeFiles/vidya_arena.dir/flutter/generated_plugin_registrant.cc.o.d
  FLAGS = -g -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral -I/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux/include -isystem /usr/include/gtk-3.0 -isystem /usr/include/pango-1.0 -isystem /usr/include/cloudproviders -isystem /usr/include/cairo -isystem /usr/include/gdk-pixbuf-2.0 -isystem /usr/include/at-spi2-atk/2.0 -isystem /usr/include/at-spi-2.0 -isystem /usr/include/atk-1.0 -isystem /usr/include/dbus-1.0 -isystem /usr/lib/dbus-1.0/include -isystem /usr/include/fribidi -isystem /usr/include/pixman-1 -isystem /usr/include/harfbuzz -isystem /usr/include/freetype2 -isystem /usr/include/libpng16 -isystem /usr/include/gio-unix-2.0 -isystem /usr/include/glib-2.0 -isystem /usr/lib/glib-2.0/include -isystem /usr/include/libmount -isystem /usr/include/blkid -isystem /usr/include/sysprof-6
  OBJECT_DIR = CMakeFiles/vidya_arena.dir
  OBJECT_FILE_DIR = CMakeFiles/vidya_arena.dir/flutter
  TARGET_COMPILE_PDB = CMakeFiles/vidya_arena.dir/
  TARGET_PDB = intermediates_do_not_run/vidya_arena.pdb


# =============================================================================
# Link build statements for EXECUTABLE target vidya_arena


#############################################
# Link the executable intermediates_do_not_run/vidya_arena

build intermediates_do_not_run/vidya_arena: CXX_EXECUTABLE_LINKER__vidya_arena_Debug CMakeFiles/vidya_arena.dir/main.cc.o CMakeFiles/vidya_arena.dir/my_application.cc.o CMakeFiles/vidya_arena.dir/flutter/generated_plugin_registrant.cc.o | plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/libflutter_linux_gtk.so /usr/lib/libgtk-3.so /usr/lib/libgdk-3.so /usr/lib/libz.so /usr/lib/libpangocairo-1.0.so /usr/lib/libcairo-gobject.so /usr/lib/libgdk_pixbuf-2.0.so /usr/lib/libatk-1.0.so /usr/lib/libpango-1.0.so /usr/lib/libcairo.so /usr/lib/libharfbuzz.so /usr/lib/libgio-2.0.so /usr/lib/libgobject-2.0.so /usr/lib/libglib-2.0.so || flutter/flutter_assemble plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so
  CONFIG = Debug
  DEP_FILE = CMakeFiles/vidya_arena.dir/link.d
  FLAGS = -g
  LINK_FLAGS = -Xlinker --dependency-file=CMakeFiles/vidya_arena.dir/link.d
  LINK_LIBRARIES = -Wl,-rpath,/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/plugins/flutter_secure_storage_linux:/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral:  plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so  -lflutter_linux_gtk  /usr/lib/libgtk-3.so  /usr/lib/libgdk-3.so  /usr/lib/libz.so  /usr/lib/libpangocairo-1.0.so  /usr/lib/libcairo-gobject.so  /usr/lib/libgdk_pixbuf-2.0.so  /usr/lib/libatk-1.0.so  /usr/lib/libpango-1.0.so  /usr/lib/libcairo.so  /usr/lib/libharfbuzz.so  /usr/lib/libgio-2.0.so  /usr/lib/libgobject-2.0.so  /usr/lib/libglib-2.0.so
  LINK_PATH = -L/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral
  OBJECT_DIR = CMakeFiles/vidya_arena.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = CMakeFiles/vidya_arena.dir/
  TARGET_FILE = intermediates_do_not_run/vidya_arena
  TARGET_PDB = intermediates_do_not_run/vidya_arena.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug && /usr/bin/ccmake -S/home/<USER>/Desktop/VidyaArena/vidya_arena/linux -B/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/Desktop/VidyaArena/vidya_arena/linux -B/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for flutter_assemble

build flutter/flutter_assemble: phony flutter/CMakeFiles/flutter_assemble /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/flutter_linux.h flutter/_phony_


#############################################
# Utility command for edit_cache

build flutter/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/flutter && /usr/bin/ccmake -S/home/<USER>/Desktop/VidyaArena/vidya_arena/linux -B/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build flutter/edit_cache: phony flutter/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build flutter/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/flutter && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/Desktop/VidyaArena/vidya_arena/linux -B/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build flutter/rebuild_cache: phony flutter/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build flutter/list_install_components: phony


#############################################
# Utility command for install

build flutter/CMakeFiles/install.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/flutter && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build flutter/install: phony flutter/CMakeFiles/install.util


#############################################
# Utility command for install/local

build flutter/CMakeFiles/install/local.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/flutter && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build flutter/install/local: phony flutter/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build flutter/CMakeFiles/install/strip.util: CUSTOM_COMMAND flutter/all
  COMMAND = cd /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/flutter && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build flutter/install/strip: phony flutter/CMakeFiles/install/strip.util


#############################################
# Phony custom command for flutter/CMakeFiles/flutter_assemble

build flutter/CMakeFiles/flutter_assemble | ${cmake_ninja_workdir}flutter/CMakeFiles/flutter_assemble: phony /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/flutter_linux.h


#############################################
# Custom command for /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/libflutter_linux_gtk.so

build /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/libflutter_linux_gtk.so /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_engine.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_call.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_response.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_value.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_view.h /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/flutter_linux.h flutter/_phony_ | ${cmake_ninja_workdir}flutter/_phony_: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/flutter && /usr/bin/cmake -E env FLUTTER_ROOT=/opt/flutter PROJECT_DIR=/home/<USER>/Desktop/VidyaArena/vidya_arena DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=/home/<USER>/Desktop/VidyaArena/vidya_arena/.dart_tool/package_config.json FLUTTER_TARGET=/home/<USER>/Desktop/VidyaArena/vidya_arena/lib/main.dart /opt/flutter/packages/flutter_tools/bin/tool_backend.sh linux-x64 Debug
  DESC = Generating /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/libflutter_linux_gtk.so, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_engine.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_call.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_method_response.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_value.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/fl_view.h, /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/flutter_linux/flutter_linux.h, _phony_
  restat = 1

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/generated_plugins.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target flutter_secure_storage_linux_plugin


#############################################
# Order-only phony target for flutter_secure_storage_linux_plugin

build cmake_object_order_depends_target_flutter_secure_storage_linux_plugin: phony || flutter/flutter_assemble

build plugins/flutter_secure_storage_linux/CMakeFiles/flutter_secure_storage_linux_plugin.dir/flutter_secure_storage_linux_plugin.cc.o: CXX_COMPILER__flutter_secure_storage_linux_plugin_unscanned_Debug /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux/flutter_secure_storage_linux_plugin.cc || cmake_object_order_depends_target_flutter_secure_storage_linux_plugin
  CONFIG = Debug
  DEFINES = -DAPPLICATION_ID=\"com.example.vidya_arena\" -DFLUTTER_PLUGIN_IMPL -Dflutter_secure_storage_linux_plugin_EXPORTS
  DEP_FILE = plugins/flutter_secure_storage_linux/CMakeFiles/flutter_secure_storage_linux_plugin.dir/flutter_secure_storage_linux_plugin.cc.o.d
  FLAGS = -g -fPIC -fvisibility=hidden -Wall -Werror -pthread
  INCLUDES = -I/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral -isystem /usr/include/libsecret-1 -isystem /usr/include/gio-unix-2.0 -isystem /usr/include/glib-2.0 -isystem /usr/lib/glib-2.0/include -isystem /usr/include/libmount -isystem /usr/include/blkid -isystem /usr/include/sysprof-6 -isystem /usr/include/tss2 -isystem /usr/include/gtk-3.0 -isystem /usr/include/pango-1.0 -isystem /usr/include/cloudproviders -isystem /usr/include/cairo -isystem /usr/include/gdk-pixbuf-2.0 -isystem /usr/include/at-spi2-atk/2.0 -isystem /usr/include/at-spi-2.0 -isystem /usr/include/atk-1.0 -isystem /usr/include/dbus-1.0 -isystem /usr/lib/dbus-1.0/include -isystem /usr/include/fribidi -isystem /usr/include/pixman-1 -isystem /usr/include/harfbuzz -isystem /usr/include/freetype2 -isystem /usr/include/libpng16
  OBJECT_DIR = plugins/flutter_secure_storage_linux/CMakeFiles/flutter_secure_storage_linux_plugin.dir
  OBJECT_FILE_DIR = plugins/flutter_secure_storage_linux/CMakeFiles/flutter_secure_storage_linux_plugin.dir
  TARGET_COMPILE_PDB = plugins/flutter_secure_storage_linux/CMakeFiles/flutter_secure_storage_linux_plugin.dir/
  TARGET_PDB = plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target flutter_secure_storage_linux_plugin


#############################################
# Link the shared library plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so

build plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so: CXX_SHARED_LIBRARY_LINKER__flutter_secure_storage_linux_plugin_Debug plugins/flutter_secure_storage_linux/CMakeFiles/flutter_secure_storage_linux_plugin.dir/flutter_secure_storage_linux_plugin.cc.o | /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/libflutter_linux_gtk.so /usr/lib/libgtk-3.so /usr/lib/libgdk-3.so /usr/lib/libz.so /usr/lib/libpangocairo-1.0.so /usr/lib/libcairo-gobject.so /usr/lib/libgdk_pixbuf-2.0.so /usr/lib/libatk-1.0.so /usr/lib/libpango-1.0.so /usr/lib/libcairo.so /usr/lib/libharfbuzz.so /usr/lib/libsecret-1.so /usr/lib/libgio-2.0.so /usr/lib/libgobject-2.0.so /usr/lib/libglib-2.0.so || flutter/flutter_assemble
  CONFIG = Debug
  DEP_FILE = plugins/flutter_secure_storage_linux/CMakeFiles/flutter_secure_storage_linux_plugin.dir/link.d
  LANGUAGE_COMPILE_FLAGS = -g
  LINK_FLAGS = -shared -Xlinker --dependency-file=plugins/flutter_secure_storage_linux/CMakeFiles/flutter_secure_storage_linux_plugin.dir/link.d
  LINK_LIBRARIES = -Wl,-rpath,/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral  -lflutter_linux_gtk  /usr/lib/libgtk-3.so  /usr/lib/libgdk-3.so  /usr/lib/libz.so  /usr/lib/libpangocairo-1.0.so  /usr/lib/libcairo-gobject.so  /usr/lib/libgdk_pixbuf-2.0.so  /usr/lib/libatk-1.0.so  /usr/lib/libpango-1.0.so  /usr/lib/libcairo.so  /usr/lib/libharfbuzz.so  /usr/lib/libsecret-1.so  /usr/lib/libgio-2.0.so  /usr/lib/libgobject-2.0.so  /usr/lib/libglib-2.0.so
  LINK_PATH = -L/home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral
  OBJECT_DIR = plugins/flutter_secure_storage_linux/CMakeFiles/flutter_secure_storage_linux_plugin.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libflutter_secure_storage_linux_plugin.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = plugins/flutter_secure_storage_linux/CMakeFiles/flutter_secure_storage_linux_plugin.dir/
  TARGET_FILE = plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so
  TARGET_PDB = plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.pdb


#############################################
# Utility command for edit_cache

build plugins/flutter_secure_storage_linux/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/plugins/flutter_secure_storage_linux && /usr/bin/ccmake -S/home/<USER>/Desktop/VidyaArena/vidya_arena/linux -B/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build plugins/flutter_secure_storage_linux/edit_cache: phony plugins/flutter_secure_storage_linux/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build plugins/flutter_secure_storage_linux/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/plugins/flutter_secure_storage_linux && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/Desktop/VidyaArena/vidya_arena/linux -B/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build plugins/flutter_secure_storage_linux/rebuild_cache: phony plugins/flutter_secure_storage_linux/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build plugins/flutter_secure_storage_linux/list_install_components: phony


#############################################
# Utility command for install

build plugins/flutter_secure_storage_linux/CMakeFiles/install.util: CUSTOM_COMMAND plugins/flutter_secure_storage_linux/all
  COMMAND = cd /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/plugins/flutter_secure_storage_linux && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build plugins/flutter_secure_storage_linux/install: phony plugins/flutter_secure_storage_linux/CMakeFiles/install.util


#############################################
# Utility command for install/local

build plugins/flutter_secure_storage_linux/CMakeFiles/install/local.util: CUSTOM_COMMAND plugins/flutter_secure_storage_linux/all
  COMMAND = cd /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/plugins/flutter_secure_storage_linux && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build plugins/flutter_secure_storage_linux/install/local: phony plugins/flutter_secure_storage_linux/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build plugins/flutter_secure_storage_linux/CMakeFiles/install/strip.util: CUSTOM_COMMAND plugins/flutter_secure_storage_linux/all
  COMMAND = cd /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/plugins/flutter_secure_storage_linux && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build plugins/flutter_secure_storage_linux/install/strip: phony plugins/flutter_secure_storage_linux/CMakeFiles/install/strip.util

# =============================================================================
# Target aliases.

build flutter_assemble: phony flutter/flutter_assemble

build flutter_secure_storage_linux_plugin: phony plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so

build libflutter_secure_storage_linux_plugin.so: phony plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so

build vidya_arena: phony intermediates_do_not_run/vidya_arena

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug

build all: phony intermediates_do_not_run/vidya_arena flutter/all plugins/flutter_secure_storage_linux/all

# =============================================================================

#############################################
# Folder: /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/flutter

build flutter/all: phony

# =============================================================================

#############################################
# Folder: /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/plugins/flutter_secure_storage_linux

build plugins/flutter_secure_storage_linux/all: phony plugins/flutter_secure_storage_linux/libflutter_secure_storage_linux_plugin.so

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/cmake_install.cmake /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/flutter/cmake_install.cmake /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/plugins/flutter_secure_storage_linux/cmake_install.cmake: RERUN_CMAKE | /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/CMakeLists.txt /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/CMakeLists.txt /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux/CMakeLists.txt /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/generated_config.cmake /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/generated_plugins.cmake /usr/share/cmake/Modules/CMakeCXXCompiler.cmake.in /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp /usr/share/cmake/Modules/CMakeCXXInformation.cmake /usr/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake/Modules/CMakeCompilerIdDetection.cmake /usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake /usr/share/cmake/Modules/CMakeDetermineCompiler.cmake /usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake /usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake /usr/share/cmake/Modules/CMakeDetermineCompilerSupport.cmake /usr/share/cmake/Modules/CMakeDetermineSystem.cmake /usr/share/cmake/Modules/CMakeFindBinUtils.cmake /usr/share/cmake/Modules/CMakeGenericSystem.cmake /usr/share/cmake/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake/Modules/CMakeLanguageInformation.cmake /usr/share/cmake/Modules/CMakeNinjaFindMake.cmake /usr/share/cmake/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake /usr/share/cmake/Modules/CMakeParseLibraryArchitecture.cmake /usr/share/cmake/Modules/CMakeSystem.cmake.in /usr/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake /usr/share/cmake/Modules/CMakeTestCompilerCommon.cmake /usr/share/cmake/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake/Modules/Compiler/Clang-CXX-CXXImportStd.cmake /usr/share/cmake/Modules/Compiler/Clang-CXX.cmake /usr/share/cmake/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/share/cmake/Modules/Compiler/Clang-FindBinUtils.cmake /usr/share/cmake/Modules/Compiler/Clang.cmake /usr/share/cmake/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/CrayClang-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Diab-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/GNU.cmake /usr/share/cmake/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /usr/share/cmake/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/OrangeC-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Renesas-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/TI-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/TIClang-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Tasking-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/FindPackageHandleStandardArgs.cmake /usr/share/cmake/Modules/FindPackageMessage.cmake /usr/share/cmake/Modules/FindPkgConfig.cmake /usr/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake /usr/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /usr/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake /usr/share/cmake/Modules/Internal/CMakeInspectCXXLinker.cmake /usr/share/cmake/Modules/Internal/FeatureTesting.cmake /usr/share/cmake/Modules/Linker/GNU-CXX.cmake /usr/share/cmake/Modules/Linker/GNU.cmake /usr/share/cmake/Modules/Platform/Linker/GNU.cmake /usr/share/cmake/Modules/Platform/Linker/Linux-GNU-CXX.cmake /usr/share/cmake/Modules/Platform/Linker/Linux-GNU.cmake /usr/share/cmake/Modules/Platform/Linux-Clang-CXX.cmake /usr/share/cmake/Modules/Platform/Linux-Determine-CXX.cmake /usr/share/cmake/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake/Modules/Platform/Linux-GNU.cmake /usr/share/cmake/Modules/Platform/Linux-Initialize.cmake /usr/share/cmake/Modules/Platform/Linux.cmake /usr/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/4.1.1/CMakeCXXCompiler.cmake CMakeFiles/4.1.1/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/CMakeLists.txt /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/CMakeLists.txt /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux/CMakeLists.txt /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/ephemeral/generated_config.cmake /home/<USER>/Desktop/VidyaArena/vidya_arena/linux/flutter/generated_plugins.cmake /usr/share/cmake/Modules/CMakeCXXCompiler.cmake.in /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp /usr/share/cmake/Modules/CMakeCXXInformation.cmake /usr/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake/Modules/CMakeCompilerIdDetection.cmake /usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake /usr/share/cmake/Modules/CMakeDetermineCompiler.cmake /usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake /usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake /usr/share/cmake/Modules/CMakeDetermineCompilerSupport.cmake /usr/share/cmake/Modules/CMakeDetermineSystem.cmake /usr/share/cmake/Modules/CMakeFindBinUtils.cmake /usr/share/cmake/Modules/CMakeGenericSystem.cmake /usr/share/cmake/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake/Modules/CMakeLanguageInformation.cmake /usr/share/cmake/Modules/CMakeNinjaFindMake.cmake /usr/share/cmake/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake /usr/share/cmake/Modules/CMakeParseLibraryArchitecture.cmake /usr/share/cmake/Modules/CMakeSystem.cmake.in /usr/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake /usr/share/cmake/Modules/CMakeTestCompilerCommon.cmake /usr/share/cmake/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake/Modules/Compiler/Clang-CXX-CXXImportStd.cmake /usr/share/cmake/Modules/Compiler/Clang-CXX.cmake /usr/share/cmake/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/share/cmake/Modules/Compiler/Clang-FindBinUtils.cmake /usr/share/cmake/Modules/Compiler/Clang.cmake /usr/share/cmake/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/CrayClang-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Diab-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/GNU.cmake /usr/share/cmake/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /usr/share/cmake/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/OrangeC-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Renesas-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/TI-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/TIClang-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Tasking-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /usr/share/cmake/Modules/FindPackageHandleStandardArgs.cmake /usr/share/cmake/Modules/FindPackageMessage.cmake /usr/share/cmake/Modules/FindPkgConfig.cmake /usr/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake /usr/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /usr/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake /usr/share/cmake/Modules/Internal/CMakeInspectCXXLinker.cmake /usr/share/cmake/Modules/Internal/FeatureTesting.cmake /usr/share/cmake/Modules/Linker/GNU-CXX.cmake /usr/share/cmake/Modules/Linker/GNU.cmake /usr/share/cmake/Modules/Platform/Linker/GNU.cmake /usr/share/cmake/Modules/Platform/Linker/Linux-GNU-CXX.cmake /usr/share/cmake/Modules/Platform/Linker/Linux-GNU.cmake /usr/share/cmake/Modules/Platform/Linux-Clang-CXX.cmake /usr/share/cmake/Modules/Platform/Linux-Determine-CXX.cmake /usr/share/cmake/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake/Modules/Platform/Linux-GNU.cmake /usr/share/cmake/Modules/Platform/Linux-Initialize.cmake /usr/share/cmake/Modules/Platform/Linux.cmake /usr/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/4.1.1/CMakeCXXCompiler.cmake CMakeFiles/4.1.1/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
