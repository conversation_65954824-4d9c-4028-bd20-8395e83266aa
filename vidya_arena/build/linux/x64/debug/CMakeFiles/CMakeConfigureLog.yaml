
---
events:
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineSystem.cmake:12 (find_program)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_UNAME"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "uname"
    candidate_directories:
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "/bin/"
    searched_directories:
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/uname"
      - "/home/<USER>/.pyenv/shims/uname"
      - "/home/<USER>/.pyenv/bin/uname"
      - "/home/<USER>/.local/bin/uname"
      - "/home/<USER>/.bun/bin/uname"
      - "/home/<USER>/.cargo/bin/uname"
      - "/usr/local/sbin/uname"
      - "/usr/local/bin/uname"
    found: "/usr/bin/uname"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Linux - 6.14.4-arch1-2 - x86_64
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeNinjaFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Program used to build from build.ninja files."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ninja-build"
      - "ninja"
      - "samu"
    candidate_directories:
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/ninja-build"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/ninja"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/samu"
      - "/home/<USER>/.pyenv/shims/ninja-build"
      - "/home/<USER>/.pyenv/shims/ninja"
      - "/home/<USER>/.pyenv/shims/samu"
      - "/home/<USER>/.pyenv/bin/ninja-build"
      - "/home/<USER>/.pyenv/bin/ninja"
      - "/home/<USER>/.pyenv/bin/samu"
      - "/home/<USER>/.local/bin/ninja-build"
      - "/home/<USER>/.local/bin/ninja"
      - "/home/<USER>/.local/bin/samu"
      - "/home/<USER>/.bun/bin/ninja-build"
      - "/home/<USER>/.bun/bin/ninja"
      - "/home/<USER>/.bun/bin/samu"
      - "/home/<USER>/.cargo/bin/ninja-build"
      - "/home/<USER>/.cargo/bin/ninja"
      - "/home/<USER>/.cargo/bin/samu"
      - "/usr/local/sbin/ninja-build"
      - "/usr/local/sbin/ninja"
      - "/usr/local/sbin/samu"
      - "/usr/local/bin/ninja-build"
      - "/usr/local/bin/ninja"
      - "/usr/local/bin/samu"
      - "/usr/bin/ninja-build"
    found: "/usr/bin/ninja"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompiler.cmake:73 (find_program)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:69 (_cmake_find_compiler)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER"
    description: "CXX compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "/usr/bin/clang++"
    candidate_directories:
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    found: "/usr/bin/clang++"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "/usr/share/cmake/Modules/"
    found: "/usr/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/bin/clang++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is Clang, found in:
        /home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/CMakeFiles/4.1.1/CompilerIdCXX/a.out
      
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-ar"
      - "ar"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "/usr/bin/llvm-ar"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-ar"
      - "/home/<USER>/.pyenv/shims/llvm-ar"
      - "/home/<USER>/.pyenv/bin/llvm-ar"
      - "/home/<USER>/.local/bin/llvm-ar"
      - "/home/<USER>/.bun/bin/llvm-ar"
      - "/home/<USER>/.cargo/bin/llvm-ar"
      - "/usr/local/sbin/llvm-ar"
      - "/usr/local/bin/llvm-ar"
      - "/var/lib/flatpak/exports/bin/llvm-ar"
      - "/usr/lib/jvm/default/bin/llvm-ar"
      - "/usr/bin/site_perl/llvm-ar"
      - "/usr/bin/vendor_perl/llvm-ar"
      - "/usr/bin/core_perl/llvm-ar"
      - "/usr/lib/rustup/bin/llvm-ar"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-ar"
    found: "/usr/bin/ar"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-ranlib"
      - "ranlib"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "/usr/bin/llvm-ranlib"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-ranlib"
      - "/home/<USER>/.pyenv/shims/llvm-ranlib"
      - "/home/<USER>/.pyenv/bin/llvm-ranlib"
      - "/home/<USER>/.local/bin/llvm-ranlib"
      - "/home/<USER>/.bun/bin/llvm-ranlib"
      - "/home/<USER>/.cargo/bin/llvm-ranlib"
      - "/usr/local/sbin/llvm-ranlib"
      - "/usr/local/bin/llvm-ranlib"
      - "/var/lib/flatpak/exports/bin/llvm-ranlib"
      - "/usr/lib/jvm/default/bin/llvm-ranlib"
      - "/usr/bin/site_perl/llvm-ranlib"
      - "/usr/bin/vendor_perl/llvm-ranlib"
      - "/usr/bin/core_perl/llvm-ranlib"
      - "/usr/lib/rustup/bin/llvm-ranlib"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-ranlib"
    found: "/usr/bin/ranlib"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-strip"
      - "strip"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "/usr/bin/llvm-strip"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-strip"
      - "/home/<USER>/.pyenv/shims/llvm-strip"
      - "/home/<USER>/.pyenv/bin/llvm-strip"
      - "/home/<USER>/.local/bin/llvm-strip"
      - "/home/<USER>/.bun/bin/llvm-strip"
      - "/home/<USER>/.cargo/bin/llvm-strip"
      - "/usr/local/sbin/llvm-strip"
      - "/usr/local/bin/llvm-strip"
      - "/var/lib/flatpak/exports/bin/llvm-strip"
      - "/usr/lib/jvm/default/bin/llvm-strip"
      - "/usr/bin/site_perl/llvm-strip"
      - "/usr/bin/vendor_perl/llvm-strip"
      - "/usr/bin/core_perl/llvm-strip"
      - "/usr/lib/rustup/bin/llvm-strip"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-strip"
    found: "/usr/bin/strip"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld.lld"
      - "ld"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "/usr/bin/ld.lld"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/ld.lld"
      - "/home/<USER>/.pyenv/shims/ld.lld"
      - "/home/<USER>/.pyenv/bin/ld.lld"
      - "/home/<USER>/.local/bin/ld.lld"
      - "/home/<USER>/.bun/bin/ld.lld"
      - "/home/<USER>/.cargo/bin/ld.lld"
      - "/usr/local/sbin/ld.lld"
      - "/usr/local/bin/ld.lld"
      - "/var/lib/flatpak/exports/bin/ld.lld"
      - "/usr/lib/jvm/default/bin/ld.lld"
      - "/usr/bin/site_perl/ld.lld"
      - "/usr/bin/vendor_perl/ld.lld"
      - "/usr/bin/core_perl/ld.lld"
      - "/usr/lib/rustup/bin/ld.lld"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/ld.lld"
    found: "/usr/bin/ld"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-nm"
      - "nm"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "/usr/bin/llvm-nm"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-nm"
      - "/home/<USER>/.pyenv/shims/llvm-nm"
      - "/home/<USER>/.pyenv/bin/llvm-nm"
      - "/home/<USER>/.local/bin/llvm-nm"
      - "/home/<USER>/.bun/bin/llvm-nm"
      - "/home/<USER>/.cargo/bin/llvm-nm"
      - "/usr/local/sbin/llvm-nm"
      - "/usr/local/bin/llvm-nm"
      - "/var/lib/flatpak/exports/bin/llvm-nm"
      - "/usr/lib/jvm/default/bin/llvm-nm"
      - "/usr/bin/site_perl/llvm-nm"
      - "/usr/bin/vendor_perl/llvm-nm"
      - "/usr/bin/core_perl/llvm-nm"
      - "/usr/lib/rustup/bin/llvm-nm"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-nm"
    found: "/usr/bin/nm"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-objdump"
      - "objdump"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "/usr/bin/llvm-objdump"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-objdump"
      - "/home/<USER>/.pyenv/shims/llvm-objdump"
      - "/home/<USER>/.pyenv/bin/llvm-objdump"
      - "/home/<USER>/.local/bin/llvm-objdump"
      - "/home/<USER>/.bun/bin/llvm-objdump"
      - "/home/<USER>/.cargo/bin/llvm-objdump"
      - "/usr/local/sbin/llvm-objdump"
      - "/usr/local/bin/llvm-objdump"
      - "/var/lib/flatpak/exports/bin/llvm-objdump"
      - "/usr/lib/jvm/default/bin/llvm-objdump"
      - "/usr/bin/site_perl/llvm-objdump"
      - "/usr/bin/vendor_perl/llvm-objdump"
      - "/usr/bin/core_perl/llvm-objdump"
      - "/usr/lib/rustup/bin/llvm-objdump"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-objdump"
    found: "/usr/bin/objdump"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-objcopy"
      - "objcopy"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "/usr/bin/llvm-objcopy"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-objcopy"
      - "/home/<USER>/.pyenv/shims/llvm-objcopy"
      - "/home/<USER>/.pyenv/bin/llvm-objcopy"
      - "/home/<USER>/.local/bin/llvm-objcopy"
      - "/home/<USER>/.bun/bin/llvm-objcopy"
      - "/home/<USER>/.cargo/bin/llvm-objcopy"
      - "/usr/local/sbin/llvm-objcopy"
      - "/usr/local/bin/llvm-objcopy"
      - "/var/lib/flatpak/exports/bin/llvm-objcopy"
      - "/usr/lib/jvm/default/bin/llvm-objcopy"
      - "/usr/bin/site_perl/llvm-objcopy"
      - "/usr/bin/vendor_perl/llvm-objcopy"
      - "/usr/bin/core_perl/llvm-objcopy"
      - "/usr/lib/rustup/bin/llvm-objcopy"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-objcopy"
    found: "/usr/bin/objcopy"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-readelf"
      - "readelf"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "/usr/bin/llvm-readelf"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-readelf"
      - "/home/<USER>/.pyenv/shims/llvm-readelf"
      - "/home/<USER>/.pyenv/bin/llvm-readelf"
      - "/home/<USER>/.local/bin/llvm-readelf"
      - "/home/<USER>/.bun/bin/llvm-readelf"
      - "/home/<USER>/.cargo/bin/llvm-readelf"
      - "/usr/local/sbin/llvm-readelf"
      - "/usr/local/bin/llvm-readelf"
      - "/var/lib/flatpak/exports/bin/llvm-readelf"
      - "/usr/lib/jvm/default/bin/llvm-readelf"
      - "/usr/bin/site_perl/llvm-readelf"
      - "/usr/bin/vendor_perl/llvm-readelf"
      - "/usr/bin/core_perl/llvm-readelf"
      - "/usr/lib/rustup/bin/llvm-readelf"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-readelf"
    found: "/usr/bin/readelf"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-dlltool"
      - "dlltool"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "/usr/bin/llvm-dlltool"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-dlltool"
      - "/home/<USER>/.pyenv/shims/llvm-dlltool"
      - "/home/<USER>/.pyenv/bin/llvm-dlltool"
      - "/home/<USER>/.local/bin/llvm-dlltool"
      - "/home/<USER>/.bun/bin/llvm-dlltool"
      - "/home/<USER>/.cargo/bin/llvm-dlltool"
      - "/usr/local/sbin/llvm-dlltool"
      - "/usr/local/bin/llvm-dlltool"
      - "/var/lib/flatpak/exports/bin/llvm-dlltool"
      - "/usr/lib/jvm/default/bin/llvm-dlltool"
      - "/usr/bin/site_perl/llvm-dlltool"
      - "/usr/bin/vendor_perl/llvm-dlltool"
      - "/usr/bin/core_perl/llvm-dlltool"
      - "/usr/lib/rustup/bin/llvm-dlltool"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-dlltool"
      - "/usr/bin/dlltool"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/dlltool"
      - "/home/<USER>/.pyenv/shims/dlltool"
      - "/home/<USER>/.pyenv/bin/dlltool"
      - "/home/<USER>/.local/bin/dlltool"
      - "/home/<USER>/.bun/bin/dlltool"
      - "/home/<USER>/.cargo/bin/dlltool"
      - "/usr/local/sbin/dlltool"
      - "/usr/local/bin/dlltool"
      - "/var/lib/flatpak/exports/bin/dlltool"
      - "/usr/lib/jvm/default/bin/dlltool"
      - "/usr/bin/site_perl/dlltool"
      - "/usr/bin/vendor_perl/dlltool"
      - "/usr/bin/core_perl/dlltool"
      - "/usr/lib/rustup/bin/dlltool"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/dlltool"
    found: false
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-addr2line"
      - "addr2line"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "/usr/bin/llvm-addr2line"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-addr2line"
      - "/home/<USER>/.pyenv/shims/llvm-addr2line"
      - "/home/<USER>/.pyenv/bin/llvm-addr2line"
      - "/home/<USER>/.local/bin/llvm-addr2line"
      - "/home/<USER>/.bun/bin/llvm-addr2line"
      - "/home/<USER>/.cargo/bin/llvm-addr2line"
      - "/usr/local/sbin/llvm-addr2line"
      - "/usr/local/bin/llvm-addr2line"
      - "/var/lib/flatpak/exports/bin/llvm-addr2line"
      - "/usr/lib/jvm/default/bin/llvm-addr2line"
      - "/usr/bin/site_perl/llvm-addr2line"
      - "/usr/bin/vendor_perl/llvm-addr2line"
      - "/usr/bin/core_perl/llvm-addr2line"
      - "/usr/lib/rustup/bin/llvm-addr2line"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-addr2line"
    found: "/usr/bin/addr2line"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "/usr/bin/tapi"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/tapi"
      - "/home/<USER>/.pyenv/shims/tapi"
      - "/home/<USER>/.pyenv/bin/tapi"
      - "/home/<USER>/.local/bin/tapi"
      - "/home/<USER>/.bun/bin/tapi"
      - "/home/<USER>/.cargo/bin/tapi"
      - "/usr/local/sbin/tapi"
      - "/usr/local/bin/tapi"
      - "/var/lib/flatpak/exports/bin/tapi"
      - "/usr/lib/jvm/default/bin/tapi"
      - "/usr/bin/site_perl/tapi"
      - "/usr/bin/vendor_perl/tapi"
      - "/usr/bin/core_perl/tapi"
      - "/usr/lib/rustup/bin/tapi"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/tapi"
    found: false
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Compiler/Clang-FindBinUtils.cmake:26 (find_program)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_AR"
    description: "LLVM archiver"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-ar-19.1"
      - "llvm-ar-19"
      - "llvm-ar19"
      - "llvm-ar"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "/usr/bin/llvm-ar-19.1"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-ar-19.1"
      - "/home/<USER>/.pyenv/shims/llvm-ar-19.1"
      - "/home/<USER>/.pyenv/bin/llvm-ar-19.1"
      - "/home/<USER>/.local/bin/llvm-ar-19.1"
      - "/home/<USER>/.bun/bin/llvm-ar-19.1"
      - "/home/<USER>/.cargo/bin/llvm-ar-19.1"
      - "/usr/local/sbin/llvm-ar-19.1"
      - "/usr/local/bin/llvm-ar-19.1"
      - "/var/lib/flatpak/exports/bin/llvm-ar-19.1"
      - "/usr/lib/jvm/default/bin/llvm-ar-19.1"
      - "/usr/bin/site_perl/llvm-ar-19.1"
      - "/usr/bin/vendor_perl/llvm-ar-19.1"
      - "/usr/bin/core_perl/llvm-ar-19.1"
      - "/usr/lib/rustup/bin/llvm-ar-19.1"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-ar-19.1"
      - "/usr/bin/llvm-ar-19"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-ar-19"
      - "/home/<USER>/.pyenv/shims/llvm-ar-19"
      - "/home/<USER>/.pyenv/bin/llvm-ar-19"
      - "/home/<USER>/.local/bin/llvm-ar-19"
      - "/home/<USER>/.bun/bin/llvm-ar-19"
      - "/home/<USER>/.cargo/bin/llvm-ar-19"
      - "/usr/local/sbin/llvm-ar-19"
      - "/usr/local/bin/llvm-ar-19"
      - "/var/lib/flatpak/exports/bin/llvm-ar-19"
      - "/usr/lib/jvm/default/bin/llvm-ar-19"
      - "/usr/bin/site_perl/llvm-ar-19"
      - "/usr/bin/vendor_perl/llvm-ar-19"
      - "/usr/bin/core_perl/llvm-ar-19"
      - "/usr/lib/rustup/bin/llvm-ar-19"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-ar-19"
      - "/usr/bin/llvm-ar19"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-ar19"
      - "/home/<USER>/.pyenv/shims/llvm-ar19"
      - "/home/<USER>/.pyenv/bin/llvm-ar19"
      - "/home/<USER>/.local/bin/llvm-ar19"
      - "/home/<USER>/.bun/bin/llvm-ar19"
      - "/home/<USER>/.cargo/bin/llvm-ar19"
      - "/usr/local/sbin/llvm-ar19"
      - "/usr/local/bin/llvm-ar19"
      - "/var/lib/flatpak/exports/bin/llvm-ar19"
      - "/usr/lib/jvm/default/bin/llvm-ar19"
      - "/usr/bin/site_perl/llvm-ar19"
      - "/usr/bin/vendor_perl/llvm-ar19"
      - "/usr/bin/core_perl/llvm-ar19"
      - "/usr/lib/rustup/bin/llvm-ar19"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-ar19"
      - "/usr/bin/llvm-ar"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-ar"
      - "/home/<USER>/.pyenv/shims/llvm-ar"
      - "/home/<USER>/.pyenv/bin/llvm-ar"
      - "/home/<USER>/.local/bin/llvm-ar"
      - "/home/<USER>/.bun/bin/llvm-ar"
      - "/home/<USER>/.cargo/bin/llvm-ar"
      - "/usr/local/sbin/llvm-ar"
      - "/usr/local/bin/llvm-ar"
      - "/var/lib/flatpak/exports/bin/llvm-ar"
      - "/usr/lib/jvm/default/bin/llvm-ar"
      - "/usr/bin/site_perl/llvm-ar"
      - "/usr/bin/vendor_perl/llvm-ar"
      - "/usr/bin/core_perl/llvm-ar"
      - "/usr/lib/rustup/bin/llvm-ar"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-ar"
    found: false
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Compiler/Clang-FindBinUtils.cmake:38 (find_program)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_RANLIB"
    description: "Generate index for LLVM archive"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "llvm-ranlib-19.1"
      - "llvm-ranlib-19"
      - "llvm-ranlib19"
      - "llvm-ranlib"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "/usr/bin/llvm-ranlib-19.1"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-ranlib-19.1"
      - "/home/<USER>/.pyenv/shims/llvm-ranlib-19.1"
      - "/home/<USER>/.pyenv/bin/llvm-ranlib-19.1"
      - "/home/<USER>/.local/bin/llvm-ranlib-19.1"
      - "/home/<USER>/.bun/bin/llvm-ranlib-19.1"
      - "/home/<USER>/.cargo/bin/llvm-ranlib-19.1"
      - "/usr/local/sbin/llvm-ranlib-19.1"
      - "/usr/local/bin/llvm-ranlib-19.1"
      - "/var/lib/flatpak/exports/bin/llvm-ranlib-19.1"
      - "/usr/lib/jvm/default/bin/llvm-ranlib-19.1"
      - "/usr/bin/site_perl/llvm-ranlib-19.1"
      - "/usr/bin/vendor_perl/llvm-ranlib-19.1"
      - "/usr/bin/core_perl/llvm-ranlib-19.1"
      - "/usr/lib/rustup/bin/llvm-ranlib-19.1"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-ranlib-19.1"
      - "/usr/bin/llvm-ranlib-19"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-ranlib-19"
      - "/home/<USER>/.pyenv/shims/llvm-ranlib-19"
      - "/home/<USER>/.pyenv/bin/llvm-ranlib-19"
      - "/home/<USER>/.local/bin/llvm-ranlib-19"
      - "/home/<USER>/.bun/bin/llvm-ranlib-19"
      - "/home/<USER>/.cargo/bin/llvm-ranlib-19"
      - "/usr/local/sbin/llvm-ranlib-19"
      - "/usr/local/bin/llvm-ranlib-19"
      - "/var/lib/flatpak/exports/bin/llvm-ranlib-19"
      - "/usr/lib/jvm/default/bin/llvm-ranlib-19"
      - "/usr/bin/site_perl/llvm-ranlib-19"
      - "/usr/bin/vendor_perl/llvm-ranlib-19"
      - "/usr/bin/core_perl/llvm-ranlib-19"
      - "/usr/lib/rustup/bin/llvm-ranlib-19"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-ranlib-19"
      - "/usr/bin/llvm-ranlib19"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-ranlib19"
      - "/home/<USER>/.pyenv/shims/llvm-ranlib19"
      - "/home/<USER>/.pyenv/bin/llvm-ranlib19"
      - "/home/<USER>/.local/bin/llvm-ranlib19"
      - "/home/<USER>/.bun/bin/llvm-ranlib19"
      - "/home/<USER>/.cargo/bin/llvm-ranlib19"
      - "/usr/local/sbin/llvm-ranlib19"
      - "/usr/local/bin/llvm-ranlib19"
      - "/var/lib/flatpak/exports/bin/llvm-ranlib19"
      - "/usr/lib/jvm/default/bin/llvm-ranlib19"
      - "/usr/bin/site_perl/llvm-ranlib19"
      - "/usr/bin/vendor_perl/llvm-ranlib19"
      - "/usr/bin/core_perl/llvm-ranlib19"
      - "/usr/lib/rustup/bin/llvm-ranlib19"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-ranlib19"
      - "/usr/bin/llvm-ranlib"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/llvm-ranlib"
      - "/home/<USER>/.pyenv/shims/llvm-ranlib"
      - "/home/<USER>/.pyenv/bin/llvm-ranlib"
      - "/home/<USER>/.local/bin/llvm-ranlib"
      - "/home/<USER>/.bun/bin/llvm-ranlib"
      - "/home/<USER>/.cargo/bin/llvm-ranlib"
      - "/usr/local/sbin/llvm-ranlib"
      - "/usr/local/bin/llvm-ranlib"
      - "/var/lib/flatpak/exports/bin/llvm-ranlib"
      - "/usr/lib/jvm/default/bin/llvm-ranlib"
      - "/usr/bin/site_perl/llvm-ranlib"
      - "/usr/bin/vendor_perl/llvm-ranlib"
      - "/usr/bin/core_perl/llvm-ranlib"
      - "/usr/lib/rustup/bin/llvm-ranlib"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/llvm-ranlib"
    found: false
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Compiler/Clang-FindBinUtils.cmake:50 (find_program)"
      - "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS"
    description: "`clang-scan-deps` dependency scanner"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "clang-scan-deps-19.1"
      - "clang-scan-deps-19"
      - "clang-scan-deps19"
      - "clang-scan-deps"
    candidate_directories:
      - "/usr/bin/"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
    searched_directories:
      - "/usr/bin/clang-scan-deps-19.1"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/clang-scan-deps-19.1"
      - "/home/<USER>/.pyenv/shims/clang-scan-deps-19.1"
      - "/home/<USER>/.pyenv/bin/clang-scan-deps-19.1"
      - "/home/<USER>/.local/bin/clang-scan-deps-19.1"
      - "/home/<USER>/.bun/bin/clang-scan-deps-19.1"
      - "/home/<USER>/.cargo/bin/clang-scan-deps-19.1"
      - "/usr/local/sbin/clang-scan-deps-19.1"
      - "/usr/local/bin/clang-scan-deps-19.1"
      - "/var/lib/flatpak/exports/bin/clang-scan-deps-19.1"
      - "/usr/lib/jvm/default/bin/clang-scan-deps-19.1"
      - "/usr/bin/site_perl/clang-scan-deps-19.1"
      - "/usr/bin/vendor_perl/clang-scan-deps-19.1"
      - "/usr/bin/core_perl/clang-scan-deps-19.1"
      - "/usr/lib/rustup/bin/clang-scan-deps-19.1"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/clang-scan-deps-19.1"
      - "/usr/bin/clang-scan-deps-19"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/clang-scan-deps-19"
      - "/home/<USER>/.pyenv/shims/clang-scan-deps-19"
      - "/home/<USER>/.pyenv/bin/clang-scan-deps-19"
      - "/home/<USER>/.local/bin/clang-scan-deps-19"
      - "/home/<USER>/.bun/bin/clang-scan-deps-19"
      - "/home/<USER>/.cargo/bin/clang-scan-deps-19"
      - "/usr/local/sbin/clang-scan-deps-19"
      - "/usr/local/bin/clang-scan-deps-19"
      - "/var/lib/flatpak/exports/bin/clang-scan-deps-19"
      - "/usr/lib/jvm/default/bin/clang-scan-deps-19"
      - "/usr/bin/site_perl/clang-scan-deps-19"
      - "/usr/bin/vendor_perl/clang-scan-deps-19"
      - "/usr/bin/core_perl/clang-scan-deps-19"
      - "/usr/lib/rustup/bin/clang-scan-deps-19"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/clang-scan-deps-19"
      - "/usr/bin/clang-scan-deps19"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/clang-scan-deps19"
      - "/home/<USER>/.pyenv/shims/clang-scan-deps19"
      - "/home/<USER>/.pyenv/bin/clang-scan-deps19"
      - "/home/<USER>/.local/bin/clang-scan-deps19"
      - "/home/<USER>/.bun/bin/clang-scan-deps19"
      - "/home/<USER>/.cargo/bin/clang-scan-deps19"
      - "/usr/local/sbin/clang-scan-deps19"
      - "/usr/local/bin/clang-scan-deps19"
      - "/var/lib/flatpak/exports/bin/clang-scan-deps19"
      - "/usr/lib/jvm/default/bin/clang-scan-deps19"
      - "/usr/bin/site_perl/clang-scan-deps19"
      - "/usr/bin/vendor_perl/clang-scan-deps19"
      - "/usr/bin/core_perl/clang-scan-deps19"
      - "/usr/lib/rustup/bin/clang-scan-deps19"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/clang-scan-deps19"
    found: "/usr/bin/clang-scan-deps"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/CMakeFiles/CMakeScratch/TryCompile-yDJrht"
      binary: "/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/CMakeFiles/CMakeScratch/TryCompile-yDJrht"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "/usr/bin/clang-scan-deps"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/CMakeFiles/CMakeScratch/TryCompile-yDJrht'
        
        Run Build Command(s): /usr/bin/ninja -v cmTC_6dc28
        [1/2] /usr/bin/clang++   -v -MD -MT CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        clang version 19.1.7
        Target: x86_64-pc-linux-gnu
        Thread model: posix
        InstalledDir: /usr/bin
        Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-pc-linux-gnu/15.1.1
        Found candidate GCC installation: /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1
        Selected GCC installation: /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1
        Candidate multilib: .;@m64
        Candidate multilib: 32;@m32
        Selected multilib: .;@m64
         (in-process)
         "/usr/bin/clang++" -cc1 -triple x86_64-pc-linux-gnu -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -fmath-errno -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu x86-64 -tune-cpu generic -debugger-tuning=gdb -fdebug-compilation-dir=/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/CMakeFiles/CMakeScratch/TryCompile-yDJrht -v -fcoverage-compilation-dir=/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/CMakeFiles/CMakeScratch/TryCompile-yDJrht -resource-dir /usr/lib/clang/19 -dependency-file CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -internal-isystem /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1 -internal-isystem /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1/x86_64-pc-linux-gnu -internal-isystem /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1/backward -internal-isystem /usr/lib/clang/19/include -internal-isystem /usr/local/include -internal-isystem /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../x86_64-pc-linux-gnu/include -internal-externc-isystem /include -internal-externc-isystem /usr/include -fdeprecated-macro -ferror-limit 19 -stack-protector 2 -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fcxx-exceptions -fexceptions -faddrsig -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o -x c++ /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        clang -cc1 version 19.1.7 based upon LLVM 19.1.7 default target x86_64-pc-linux-gnu
        ignoring nonexistent directory "/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../x86_64-pc-linux-gnu/include"
        ignoring nonexistent directory "/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1
         /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1/x86_64-pc-linux-gnu
         /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1/backward
         /usr/lib/clang/19/include
         /usr/local/include
         /usr/include
        End of search list.
        [2/2] : && /usr/bin/clang++  -v -Wl,-v CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_6dc28   && :
        clang version 19.1.7
        Target: x86_64-pc-linux-gnu
        Thread model: posix
        InstalledDir: /usr/bin
        Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-pc-linux-gnu/15.1.1
        Found candidate GCC installation: /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1
        Selected GCC installation: /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1
        Candidate multilib: .;@m64
        Candidate multilib: 32;@m32
        Selected multilib: .;@m64
         "/usr/bin/ld" --hash-style=gnu --build-id --eh-frame-hdr -m elf_x86_64 -pie -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_6dc28 /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64/Scrt1.o /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64/crti.o /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/crtbeginS.o -L/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1 -L/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/lib -L/usr/lib -v CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/crtendS.o /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64/crtn.o
        GNU ld (GNU Binutils) 2.44.0
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "/usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1]
          add: [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1/x86_64-pc-linux-gnu]
          add: [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1/backward]
          add: [/usr/lib/clang/19/include]
          add: [/usr/local/include]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1] ==> [/usr/include/c++/15.1.1]
        collapse include dir [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1/x86_64-pc-linux-gnu] ==> [/usr/include/c++/15.1.1/x86_64-pc-linux-gnu]
        collapse include dir [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1/backward] ==> [/usr/include/c++/15.1.1/backward]
        collapse include dir [/usr/lib/clang/19/include] ==> [/usr/lib/clang/19/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/include/c++/15.1.1;/usr/include/c++/15.1.1/x86_64-pc-linux-gnu;/usr/include/c++/15.1.1/backward;/usr/lib/clang/19/include;/usr/local/include;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "/usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/CMakeFiles/CMakeScratch/TryCompile-yDJrht']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/bin/ninja -v cmTC_6dc28]
        ignore line: [[1/2] /usr/bin/clang++   -v -MD -MT CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [clang version 19.1.7]
        ignore line: [Target: x86_64-pc-linux-gnu]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /usr/bin]
        ignore line: [Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-pc-linux-gnu/15.1.1]
        ignore line: [Found candidate GCC installation: /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1]
        ignore line: [Selected GCC installation: /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1]
        ignore line: [Candidate multilib: .]
        ignore line: [@m64]
        ignore line: [Candidate multilib: 32]
        ignore line: [@m32]
        ignore line: [Selected multilib: .]
        ignore line: [@m64]
        ignore line: [ (in-process)]
        ignore line: [ "/usr/bin/clang++" -cc1 -triple x86_64-pc-linux-gnu -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -fmath-errno -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu x86-64 -tune-cpu generic -debugger-tuning=gdb -fdebug-compilation-dir=/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/CMakeFiles/CMakeScratch/TryCompile-yDJrht -v -fcoverage-compilation-dir=/home/<USER>/Desktop/VidyaArena/vidya_arena/build/linux/x64/debug/CMakeFiles/CMakeScratch/TryCompile-yDJrht -resource-dir /usr/lib/clang/19 -dependency-file CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -internal-isystem /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1 -internal-isystem /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1/x86_64-pc-linux-gnu -internal-isystem /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1/backward -internal-isystem /usr/lib/clang/19/include -internal-isystem /usr/local/include -internal-isystem /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../x86_64-pc-linux-gnu/include -internal-externc-isystem /include -internal-externc-isystem /usr/include -fdeprecated-macro -ferror-limit 19 -stack-protector 2 -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fcxx-exceptions -fexceptions -faddrsig -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o -x c++ /usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [clang -cc1 version 19.1.7 based upon LLVM 19.1.7 default target x86_64-pc-linux-gnu]
        ignore line: [ignoring nonexistent directory "/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../x86_64-pc-linux-gnu/include"]
        ignore line: [ignoring nonexistent directory "/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1]
        ignore line: [ /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1/x86_64-pc-linux-gnu]
        ignore line: [ /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../include/c++/15.1.1/backward]
        ignore line: [ /usr/lib/clang/19/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [[2/2] : && /usr/bin/clang++  -v -Wl -v CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_6dc28   && :]
        ignore line: [clang version 19.1.7]
        ignore line: [Target: x86_64-pc-linux-gnu]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /usr/bin]
        ignore line: [Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-pc-linux-gnu/15.1.1]
        ignore line: [Found candidate GCC installation: /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1]
        ignore line: [Selected GCC installation: /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1]
        ignore line: [Candidate multilib: .]
        ignore line: [@m64]
        ignore line: [Candidate multilib: 32]
        ignore line: [@m32]
        ignore line: [Selected multilib: .]
        ignore line: [@m64]
        link line: [ "/usr/bin/ld" --hash-style=gnu --build-id --eh-frame-hdr -m elf_x86_64 -pie -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_6dc28 /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64/Scrt1.o /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64/crti.o /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/crtbeginS.o -L/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1 -L/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64 -L/lib/../lib64 -L/usr/lib/../lib64 -L/lib -L/usr/lib -v CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/crtendS.o /usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64/crtn.o]
          arg [/usr/bin/ld] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [-m] ==> ignore
          arg [elf_x86_64] ==> ignore
          arg [-pie] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_6dc28] ==> ignore
          arg [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64/Scrt1.o] ==> obj [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64/Scrt1.o]
          arg [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64/crti.o] ==> obj [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64/crti.o]
          arg [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/crtbeginS.o] ==> obj [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/crtbeginS.o]
          arg [-L/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1] ==> dir [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1]
          arg [-L/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64] ==> dir [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64]
          arg [-L/lib/../lib64] ==> dir [/lib/../lib64]
          arg [-L/usr/lib/../lib64] ==> dir [/usr/lib/../lib64]
          arg [-L/lib] ==> dir [/lib]
          arg [-L/usr/lib] ==> dir [/usr/lib]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_6dc28.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lm] ==> lib [m]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/crtendS.o] ==> obj [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/crtendS.o]
          arg [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64/crtn.o] ==> obj [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64/crtn.o]
        linker tool for 'CXX': /usr/bin/ld
        collapse obj [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64/Scrt1.o] ==> [/usr/lib64/Scrt1.o]
        collapse obj [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64/crti.o] ==> [/usr/lib64/crti.o]
        collapse obj [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/crtbeginS.o] ==> [/usr/lib64/gcc/x86_64-pc-linux-gnu/15.1.1/crtbeginS.o]
        collapse obj [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/crtendS.o] ==> [/usr/lib64/gcc/x86_64-pc-linux-gnu/15.1.1/crtendS.o]
        collapse obj [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64/crtn.o] ==> [/usr/lib64/crtn.o]
        collapse library dir [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1] ==> [/usr/lib64/gcc/x86_64-pc-linux-gnu/15.1.1]
        collapse library dir [/usr/bin/../lib64/gcc/x86_64-pc-linux-gnu/15.1.1/../../../../lib64] ==> [/usr/lib64]
        collapse library dir [/lib/../lib64] ==> [/lib64]
        collapse library dir [/usr/lib/../lib64] ==> [/usr/lib64]
        collapse library dir [/lib] ==> [/lib]
        collapse library dir [/usr/lib] ==> [/usr/lib]
        implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
        implicit objs: [/usr/lib64/Scrt1.o;/usr/lib64/crti.o;/usr/lib64/gcc/x86_64-pc-linux-gnu/15.1.1/crtbeginS.o;/usr/lib64/gcc/x86_64-pc-linux-gnu/15.1.1/crtendS.o;/usr/lib64/crtn.o]
        implicit dirs: [/usr/lib64/gcc/x86_64-pc-linux-gnu/15.1.1;/usr/lib64;/lib64;/lib;/usr/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "/usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "/usr/bin/ld" "-v"
      GNU ld (GNU Binutils) 2.44.0
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:69 (find_program)"
      - "flutter/CMakeLists.txt:24 (find_package)"
    mode: "program"
    variable: "PKG_CONFIG_EXECUTABLE"
    description: "pkg-config executable"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "pkg-config"
      - "pkgconf"
    candidate_directories:
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/"
      - "/home/<USER>/.pyenv/shims/"
      - "/home/<USER>/.pyenv/bin/"
      - "/home/<USER>/.local/bin/"
      - "/home/<USER>/.bun/bin/"
      - "/home/<USER>/.cargo/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/bin/"
      - "/usr/bin/"
      - "/var/lib/flatpak/exports/bin/"
      - "/usr/lib/jvm/default/bin/"
      - "/usr/bin/site_perl/"
      - "/usr/bin/vendor_perl/"
      - "/usr/bin/core_perl/"
      - "/usr/lib/rustup/bin/"
      - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand/"
      - "/usr/local/bin/"
      - "/usr/local/sbin/"
      - "/usr/local/"
      - "/usr/bin/"
      - "/usr/sbin/"
      - "/usr/"
      - "/bin/"
      - "/sbin/"
      - "/usr/X11R6/bin/"
      - "/usr/X11R6/sbin/"
      - "/usr/X11R6/"
      - "/usr/pkg/bin/"
      - "/usr/pkg/sbin/"
      - "/usr/pkg/"
      - "/opt/bin/"
      - "/opt/sbin/"
      - "/opt/"
    searched_directories:
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/pkg-config"
      - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims/pkgconf"
      - "/home/<USER>/.pyenv/shims/pkg-config"
      - "/home/<USER>/.pyenv/shims/pkgconf"
      - "/home/<USER>/.pyenv/bin/pkg-config"
      - "/home/<USER>/.pyenv/bin/pkgconf"
      - "/home/<USER>/.local/bin/pkg-config"
      - "/home/<USER>/.local/bin/pkgconf"
      - "/home/<USER>/.bun/bin/pkg-config"
      - "/home/<USER>/.bun/bin/pkgconf"
      - "/home/<USER>/.cargo/bin/pkg-config"
      - "/home/<USER>/.cargo/bin/pkgconf"
      - "/usr/local/sbin/pkg-config"
      - "/usr/local/sbin/pkgconf"
      - "/usr/local/bin/pkg-config"
      - "/usr/local/bin/pkgconf"
    found: "/usr/bin/pkg-config"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:25 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GTK_gtk-3"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gtk-3"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libgtk-3.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:25 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GTK_gdk-3"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gdk-3"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libgdk-3.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:25 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GTK_z"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "z"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libz.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:25 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GTK_pangocairo-1.0"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "pangocairo-1.0"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libpangocairo-1.0.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:25 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GTK_cairo-gobject"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "cairo-gobject"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libcairo-gobject.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:25 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GTK_gdk_pixbuf-2.0"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gdk_pixbuf-2.0"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libgdk_pixbuf-2.0.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:25 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GTK_atk-1.0"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "atk-1.0"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libatk-1.0.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:25 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GTK_pango-1.0"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "pango-1.0"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libpango-1.0.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:25 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GTK_cairo"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "cairo"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libcairo.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:25 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GTK_harfbuzz"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "harfbuzz"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libharfbuzz.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:25 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GTK_gio-2.0"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gio-2.0"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libgio-2.0.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:25 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GTK_gobject-2.0"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gobject-2.0"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libgobject-2.0.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:25 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GTK_glib-2.0"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "glib-2.0"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libglib-2.0.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:26 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GLIB_glib-2.0"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "glib-2.0"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libglib-2.0.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:27 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GIO_gio-2.0"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gio-2.0"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libgio-2.0.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:27 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GIO_gobject-2.0"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gobject-2.0"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libgobject-2.0.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/CMakeLists.txt:27 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_GIO_glib-2.0"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "glib-2.0"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libglib-2.0.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux/CMakeLists.txt:15 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_LIBSECRET_secret-1"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "secret-1"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libsecret-1.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux/CMakeLists.txt:15 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_LIBSECRET_gio-2.0"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gio-2.0"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libgio-2.0.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux/CMakeLists.txt:15 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_LIBSECRET_gobject-2.0"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gobject-2.0"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libgobject-2.0.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
  -
    kind: "find-v1"
    backtrace:
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:307 (find_library)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:357 (_pkg_find_libs)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:695 (_pkg_recalculate)"
      - "/usr/share/cmake/Modules/FindPkgConfig.cmake:867 (_pkg_check_modules_internal)"
      - "flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/linux/CMakeLists.txt:15 (pkg_check_modules)"
    mode: "library"
    variable: "pkgcfg_lib_LIBSECRET_glib-2.0"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "glib-2.0"
    candidate_directories:
      - "/usr/lib/"
    found: "/usr/lib/libglib-2.0.so"
    search_context:
      ENV{PATH}:
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.pyenv/plugins/pyenv-virtualenv/shims"
        - "/home/<USER>/.pyenv/bin"
        - "/home/<USER>/.local/bin"
        - "/home/<USER>/.bun/bin"
        - "/home/<USER>/.cargo/bin"
        - "/home/<USER>/.local/bin"
        - "/usr/local/sbin"
        - "/usr/local/bin"
        - "/usr/bin"
        - "/var/lib/flatpak/exports/bin"
        - "/usr/lib/jvm/default/bin"
        - "/usr/bin/site_perl"
        - "/usr/bin/vendor_perl"
        - "/usr/bin/core_perl"
        - "/usr/lib/rustup/bin"
        - "/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand"
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/usr/local"
        - "/usr"
        - "/"
        - "/usr"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "/usr/lib/X11"
...
