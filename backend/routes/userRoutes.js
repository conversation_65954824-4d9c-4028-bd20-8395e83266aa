const express = require('express');
const router = express.Router();
const {
  registerUser,
  loginUser,
  getUserProfile,
  checkEmailExists
} = require('../controllers/userController');
const {
  validateRegistration,
  validateLogin,
  handleValidationErrors
} = require('../middleware/validation');
const { authenticateToken } = require('../middleware/auth');

// Public routes
router.post('/register', validateRegistration, handleValidationErrors, registerUser);
router.post('/login', validateLogin, handleValidationErrors, loginUser);
router.get('/check-email', checkEmailExists);

// Protected routes
router.get('/profile', authenticateToken, getUserProfile);

module.exports = router;
