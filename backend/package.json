{"name": "vidya-arena-backend", "version": "1.0.0", "description": "Backend API for VidyaArena Flutter app", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-db.js"}, "keywords": ["vidya-arena", "api", "backend"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "express-validator": "^7.0.1", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2"}}