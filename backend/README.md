# VidyaArena Backend API

A Node.js Express backend for the VidyaArena Flutter application with PostgreSQL database.

## Features

- User registration and authentication
- JWT-based authorization
- Password hashing with bcrypt
- Input validation
- Rate limiting
- CORS support
- Environment-based configuration
- PostgreSQL database integration

## Prerequisites

- Node.js (v16 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

## Setup Instructions

### 1. Install Dependencies

```bash
cd backend
npm install
```

### 2. Database Setup

1. Install PostgreSQL on your system
2. Create a database named `vidya_arena`:

```sql
CREATE DATABASE vidya_arena;
```

3. Update the `.env` file with your database credentials

### 3. Environment Configuration

1. Copy `.env.example` to `.env`:
```bash
cp .env.example .env
```

2. Update the `.env` file with your configuration:
```
DB_HOST=localhost
DB_PORT=5432
DB_NAME=vidya_arena
DB_USER=your_username
DB_PASSWORD=your_password
JWT_SECRET=your_super_secret_jwt_key
```

### 4. Initialize Database

```bash
npm run init-db
```

### 5. Start the Server

For development:
```bash
npm run dev
```

For production:
```bash
npm start
```

The server will start on `http://localhost:3000`

## API Endpoints

### Health Check
- `GET /health` - Check if the API is running

### User Management
- `POST /api/users/register` - Register a new user
- `POST /api/users/login` - User login
- `GET /api/users/profile` - Get user profile (requires authentication)
- `GET /api/users/check-email?email=<EMAIL>` - Check if email exists

## API Usage Examples

### Register User
```bash
curl -X POST http://localhost:3000/api/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "password123",
    "mobile": "+**********",
    "dob": "1990-01-01",
    "gender": "male"
  }'
```

### Login User
```bash
curl -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Get Profile (with token)
```bash
curl -X GET http://localhost:3000/api/users/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Production Deployment

For production deployment, consider:

1. Use environment variables for all sensitive configuration
2. Set up SSL/TLS certificates
3. Use a process manager like PM2
4. Set up database connection pooling
5. Configure proper logging
6. Set up monitoring and health checks

## Security Features

- Password hashing with bcrypt (12 rounds)
- JWT token authentication
- Rate limiting (100 requests per 15 minutes per IP)
- Input validation and sanitization
- CORS protection
- Helmet.js security headers
