# VidyaArena Setup Guide

This guide will help you set up the VidyaArena project with a hosted database backend.

## Project Structure

```
VidyaArena/
├── backend/          # Node.js Express API server
├── vidya_arena/      # Flutter mobile app
└── setup.md         # This file
```

## Prerequisites

### For Backend
- Node.js (v16 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

### For Flutter App
- Flutter SDK (latest stable)
- Dart SDK
- Android Studio / VS Code
- Android SDK (for Android development)
- Xcode (for iOS development on macOS)

## Setup Instructions

### 1. Database Setup

#### Install PostgreSQL
- **Ubuntu/Debian**: `sudo apt-get install postgresql postgresql-contrib`
- **macOS**: `brew install postgresql`
- **Windows**: Download from [PostgreSQL official website](https://www.postgresql.org/download/)

#### Create Database
```bash
# Start PostgreSQL service
sudo service postgresql start  # Linux
brew services start postgresql  # macOS

# Connect to PostgreSQL
sudo -u postgres psql

# Create database and user
CREATE DATABASE vidya_arena;
CREATE USER vidya_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE vidya_arena TO vidya_user;
\q
```

### 2. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Edit .env file with your database credentials
nano .env
```

Update the `.env` file:
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=vidya_arena
DB_USER=vidya_user
DB_PASSWORD=your_secure_password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_change_in_production
```

#### Initialize Database
```bash
# Create database tables
npm run init-db
```

#### Start Backend Server
```bash
# Development mode (with auto-reload)
npm run dev

# Production mode
npm start
```

The backend will be available at `http://localhost:3000`

### 3. Flutter App Setup

```bash
# Navigate to Flutter app directory
cd vidya_arena

# Get dependencies
flutter pub get

# Run the app
flutter run
```

## API Endpoints

### Health Check
- `GET /health` - Check if API is running

### User Management
- `POST /api/users/register` - Register new user
- `POST /api/users/login` - User login
- `GET /api/users/profile` - Get user profile (requires auth)
- `GET /api/users/check-email` - Check if email exists

## Testing the Setup

### 1. Test Backend
```bash
# Check health endpoint
curl http://localhost:3000/health

# Test user registration
curl -X POST http://localhost:3000/api/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "password": "password123",
    "mobile": "+**********",
    "dob": "1990-01-01",
    "gender": "male"
  }'
```

### 2. Test Flutter App
1. Start the backend server (`npm run dev`)
2. Run the Flutter app (`flutter run`)
3. Try registering a new user
4. Try logging in with the registered user

## Environment Configuration

### Development
- Backend runs on `http://localhost:3000`
- Flutter app connects to localhost backend
- Database runs locally

### Production Deployment

#### Backend Options
1. **Heroku**: Easy deployment with PostgreSQL addon
2. **DigitalOcean App Platform**: Simple Node.js deployment
3. **AWS EC2**: Full control with RDS for database
4. **Railway**: Modern deployment platform

#### Database Options
1. **Heroku Postgres**: Managed PostgreSQL
2. **AWS RDS**: Scalable database service
3. **DigitalOcean Managed Databases**: Simple managed database
4. **Supabase**: PostgreSQL with additional features

#### Flutter App
1. Build APK for Android: `flutter build apk`
2. Build for iOS: `flutter build ios`
3. Deploy to app stores

## Configuration for Production

### Backend Environment Variables
```env
NODE_ENV=production
PORT=3000
DB_HOST=your-production-db-host
DB_NAME=your-production-db-name
DB_USER=your-production-db-user
DB_PASSWORD=your-production-db-password
JWT_SECRET=your-super-secure-production-jwt-secret
ALLOWED_ORIGINS=https://your-app-domain.com
```

### Flutter App Configuration
Update `lib/services/api_service.dart`:
```dart
static const String _baseUrl = kDebugMode 
    ? 'http://localhost:3000/api' 
    : 'https://your-production-api.com/api';
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check PostgreSQL is running
   - Verify database credentials in `.env`
   - Ensure database exists

2. **Flutter HTTP Error**
   - Check backend server is running
   - Verify API URL in Flutter app
   - Check network permissions in Android manifest

3. **CORS Issues**
   - Update `ALLOWED_ORIGINS` in backend `.env`
   - Ensure proper CORS configuration

### Logs
- Backend logs: Check terminal where `npm run dev` is running
- Flutter logs: Check terminal where `flutter run` is running
- Database logs: Check PostgreSQL logs

## Security Notes

1. Change default JWT secret in production
2. Use strong database passwords
3. Enable SSL/TLS in production
4. Implement rate limiting (already included)
5. Validate all inputs (already included)
6. Use HTTPS in production

## Next Steps

1. Set up CI/CD pipeline
2. Add more API endpoints as needed
3. Implement push notifications
4. Add analytics and monitoring
5. Set up automated backups
